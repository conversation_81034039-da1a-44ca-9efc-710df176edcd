
# 日内选股策略 - 基于策略模板框架
from gm.api import *
import pandas as pd

# ==================== 配置参数 ====================
STRATEGY_ID = '58d5321f-5d4b-11f0-b220-00e2699251ab'
TOKEN = '787cec949649eb140ea4ccccb14523af76110c8f'

# 选股条件参数
PRICE_CHANGE_MIN = 0.02      # 涨幅下限 2%
PRICE_CHANGE_MAX = 0.05      # 涨幅上限 5%
VOLUME_RATIO_MIN = 2.0       # 量比最小值 2.0倍
TURNOVER_MIN = 0.05          # 换手率下限 5%
TURNOVER_MAX = 0.10          # 换手率上限 10%
MARKET_CAP_MIN = 300000      # 流通市值下限 30亿(万元)
MARKET_CAP_MAX = 2000000     # 流通市值上限 200亿(万元)
RSI_MIN = 50                 # RSI最小值

# 均线参数
MA_SHORT = 5                 # 短期均线
MA_MEDIUM = 10               # 中期均线
MA_LONG = 20                 # 长期均线

# 止盈止损参数（调整为更合理的数值）
FIXED_TAKE_PROFIT = 0.03     # 固定止盈 5% (降低止盈门槛)
FLOATING_TAKE_PROFIT = 0.02  # 浮动止盈 2% (突破当日高点后回落2%)
FIXED_STOP_LOSS = 0.015       # 固定止损 3% (降低止损，给股票更多空间)

# 策略参数
INITIAL_CASH = 10000000       # 初始资金
POSITION_SIZE_RATIO = 0.05   # 单只股票持仓比例 20%
MAX_POSITIONS = 20            # 最大持仓数量

# 回测时间
BACKTEST_START_DATE = '2025-01-01'
BACKTEST_END_DATE = '2025-09-01'

# 全市场股票池 - 将在init中动态获取
SYMBOLS = []  # 将动态获取沪深两市所有A股

# ==================== 技术指标计算函数 ====================
def calculate_ma(prices, period):
    """计算移动平均线"""
    if len(prices) < period:
        return None
    return sum(prices[-period:]) / period

def calculate_rsi(prices, period=14):
    """计算RSI指标"""
    if len(prices) < period + 1:
        return None
    
    gains = []
    losses = []
    
    for i in range(1, len(prices)):
        change = prices[i] - prices[i-1]
        if change > 0:
            gains.append(change)
            losses.append(0)
        else:
            gains.append(0)
            losses.append(-change)
    
    if len(gains) < period:
        return None
    
    avg_gain = sum(gains[-period:]) / period
    avg_loss = sum(losses[-period:]) / period
    
    if avg_loss == 0:
        return 100
    
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def calculate_volume_ratio(current_volume, avg_volume):
    """计算量比 - 当日成交量除以近5日平均成交量"""
    if avg_volume <= 0:
        return 0
    return current_volume / avg_volume

# ==================== 财务数据获取函数 ====================
def get_turnover_rate(symbol, trade_date):
    """
    使用stk_get_daily_basic获取当天换手率数据

    Args:
        symbol: 股票代码，如'SHSE.600519'
        trade_date: 交易日期，如'2025-07-01'

    Returns:
        float: 换手率（小数形式），如0.05表示5%
        None: 获取失败时返回None
    """
    try:
        # 使用stk_get_daily_basic_pt获取换手率数据
        # 关键技巧：掘金API需要查询日期+1才能获取当天换手率数据
        # 这是掘金系统的特殊机制，确保数据时间一致性
        from datetime import datetime, timedelta
        turnover_date = (datetime.strptime(trade_date, '%Y-%m-%d') + timedelta(days=1)).strftime('%Y-%m-%d')

        # 优先使用日期+1获取当天数据
        basic_data = stk_get_daily_basic_pt(
            symbols=[symbol],
            fields='turnrate',
            trade_date=turnover_date,  # 关键：使用日期+1
            df=True
        )

        if not basic_data.empty:
            # turnrate字段返回的是百分比形式，需要除以100转换为小数
            turnover_rate = basic_data.iloc[0]['turnrate'] / 100
            return turnover_rate
        else:
            # 如果明日数据为空，回退使用当日数据
            fallback_data = stk_get_daily_basic_pt(
                symbols=[symbol],
                fields='turnrate',
                trade_date=trade_date,  # 使用原始日期
                df=True
            )

            if not fallback_data.empty:
                turnover_rate = fallback_data.iloc[0]['turnrate'] / 100
                return turnover_rate
            else:
                return None

    except Exception as e:
        print(f"    ⚠️ 获取换手率异常: {e}")
        return None



# ==================== 批量数据预取模块 ====================
def batch_fetch_financial_data(context, symbols, trade_date):
    """
    批量预取财务数据 - 获取当天换手率数据

    Args:
        context: 策略上下文
        symbols: 股票代码列表
        trade_date: 交易日期

    Returns:
        dict: 包含市值和换手率数据的字典
    """
    print(f"  📊 批量预取财务数据: {len(symbols)}只股票 (日期: {trade_date})")

    financial_data = {
        'market_cap': {},     # 市值数据
        'turnover_rate': {},  # 换手率数据
        'fetch_time': trade_date
    }

    # 分批处理，每批500只股票
    batch_size = 500
    total_batches = (len(symbols) + batch_size - 1) // batch_size
    print(f"  🔄 将分{total_batches}批处理，每批{batch_size}只股票")

    try:
        # 分批获取市值数据 - 增强容错处理
        print("    💰 获取市值数据...")
        for batch_idx in range(total_batches):
            start_idx = batch_idx * batch_size
            end_idx = min((batch_idx + 1) * batch_size, len(symbols))
            batch_symbols = symbols[start_idx:end_idx]

            print(f"      处理第{batch_idx + 1}/{total_batches}批: {len(batch_symbols)}只股票")

            try:
                mktvalue_data = stk_get_daily_mktvalue_pt(
                    symbols=batch_symbols,
                    fields='a_mv',
                    trade_date=trade_date,
                    df=True
                )

                if mktvalue_data is not None and not mktvalue_data.empty:
                    for _, row in mktvalue_data.iterrows():
                        symbol = row['symbol']
                        market_cap = row['a_mv']  # 元（根据官方文档）

                        # 数据有效性检查
                        if market_cap is None or market_cap <= 0:
                            continue

                        # 转换单位：从元转换为万元（策略中使用万元作为单位）
                        market_cap = market_cap / 10000  # 元 -> 万元

                        financial_data['market_cap'][symbol] = market_cap
                else:
                    print(f"      ⚠️ 第{batch_idx + 1}批市值数据为空")
            except Exception as e:
                print(f"      ❌ 第{batch_idx + 1}批市值数据获取失败: {e}")
                continue

        print(f"    ✅ 市值数据获取完成: {len(financial_data['market_cap'])}只")

        # 分批获取换手率数据 - 关键技巧：使用日期+1获取当天数据
        # 重要：掘金API特殊机制，查询明天的日期才能获取今天的换手率数据
        # 例如：要获取7月23日的换手率，需要查询7月24日的数据
        from datetime import datetime, timedelta
        turnover_date = (datetime.strptime(trade_date, '%Y-%m-%d') + timedelta(days=1)).strftime('%Y-%m-%d')
        print(f"    🔄 获取换手率数据 (查询日期: {turnover_date} 获取 {trade_date} 的数据)...")

        for batch_idx in range(total_batches):
            start_idx = batch_idx * batch_size
            end_idx = min((batch_idx + 1) * batch_size, len(symbols))
            batch_symbols = symbols[start_idx:end_idx]

            print(f"      处理第{batch_idx + 1}/{total_batches}批换手率: {len(batch_symbols)}只股票")

            try:
                # 优先使用日期+1获取当天数据
                basic_data = stk_get_daily_basic_pt(
                    symbols=batch_symbols,
                    fields='turnrate',
                    trade_date=turnover_date,  # 关键：使用日期+1获取当天数据
                    df=True
                )

                current_batch_count = 0
                if basic_data is not None and not basic_data.empty:
                    for _, row in basic_data.iterrows():
                        symbol = row['symbol']
                        turnover_rate = row['turnrate']

                        # 数据有效性检查
                        if turnover_rate is None or turnover_rate <= 0:
                            continue

                        turnover_rate = turnover_rate / 100  # 转换为小数
                        financial_data['turnover_rate'][symbol] = turnover_rate
                        current_batch_count += 1

                # 如果明日数据为空，回退使用当日数据
                if current_batch_count == 0:
                    print(f"      🔄 明日数据为空，回退使用当日数据: {trade_date}")
                    fallback_data = stk_get_daily_basic_pt(
                        symbols=batch_symbols,
                        fields='turnrate',
                        trade_date=trade_date,  # 使用原始日期
                        df=True
                    )

                    if fallback_data is not None and not fallback_data.empty:
                        for _, row in fallback_data.iterrows():
                            symbol = row['symbol']
                            turnover_rate = row['turnrate']

                            # 数据有效性检查
                            if turnover_rate is None or turnover_rate <= 0:
                                continue

                            turnover_rate = turnover_rate / 100  # 转换为小数
                            financial_data['turnover_rate'][symbol] = turnover_rate
                            current_batch_count += 1

                if current_batch_count == 0:
                    print(f"      ⚠️ 第{batch_idx + 1}批换手率数据为空")

            except Exception as e:
                print(f"      ❌ 第{batch_idx + 1}批换手率数据获取失败: {e}")
                continue

        print(f"    ✅ 换手率数据获取完成: {len(financial_data['turnover_rate'])}只 (实际获取 {trade_date} 的数据)")

    except Exception as e:
        print(f"    ❌ 批量获取财务数据异常: {e}")

    # 数据获取结果检查
    market_cap_count = len(financial_data['market_cap'])
    turnover_count = len(financial_data['turnover_rate'])

    print(f"  ✅ 财务数据预取完成: 市值{market_cap_count}只, 换手率{turnover_count}只")

    # 如果数据获取失败，给出警告
    if market_cap_count == 0 or turnover_count == 0:
        print(f"  ⚠️ 财务数据获取不完整，可能影响选股效果")
        print(f"    - 市值数据: {market_cap_count}/{len(symbols)}只")
        print(f"    - 换手率数据: {turnover_count}/{len(symbols)}只")
        print(f"    - 建议检查网络连接和API权限")

    return financial_data

def get_cached_financial_data(context, symbol, data_type):
    """从缓存中获取财务数据"""
    return context.financial_cache.get(data_type, {}).get(symbol, None)



def get_market_cap(symbol, trade_date):
    """
    使用stk_get_daily_mktvalue_pt获取官方市值数据

    Args:
        symbol: 股票代码，如'SHSE.600519'
        trade_date: 交易日期，如'2025-07-01'

    Returns:
        float: 流通市值（万元）
        None: 获取失败时返回None
    """
    try:
        # 使用stk_get_daily_mktvalue_pt获取市值数据
        mktvalue_data = stk_get_daily_mktvalue_pt(
            symbols=[symbol],
            fields='a_mv',  # 流通市值字段
            trade_date=trade_date,
            df=True
        )

        if not mktvalue_data.empty:
            # a_mv字段返回的是元（根据官方文档），转换为万元
            market_cap = mktvalue_data.iloc[0]['a_mv'] / 10000  # 元 -> 万元
            return market_cap
        else:
            return None

    except Exception as e:
        print(f"    ⚠️ 获取市值异常: {e}")
        return None

# ==================== 策略函数 ====================
def init(context):
    """策略初始化"""
    print("="*70)
    print("                全市场日内选股策略启动")
    print("="*70)

    # 动态获取全市场股票池（基于指数成分股）
    global SYMBOLS
    try:
        print("🔄 正在获取全市场股票池...")

        # 获取当前日期
        today = context.now.strftime('%Y-%m-%d')
        last_day = get_previous_n_trading_dates(exchange='SHSE', date=today, n=1)[0]

        # 获取沪深两市所有A股
        print("  📊 获取沪深两市所有A股...")

        # 方法1：使用get_instruments获取所有股票 - 增强容错处理
        try:
            print("    🔄 尝试获取沪深两市A股...")
            # 获取沪市A股
            sh_stocks = get_instruments(exchanges='SHSE', sec_types=1, df=True)
            # 获取深市A股
            sz_stocks = get_instruments(exchanges='SZSE', sec_types=1, df=True)

            print(f"    📊 沪市股票获取结果: {len(sh_stocks) if sh_stocks is not None and not sh_stocks.empty else 0}只")
            print(f"    📊 深市股票获取结果: {len(sz_stocks) if sz_stocks is not None and not sz_stocks.empty else 0}只")

            if sh_stocks is not None and not sh_stocks.empty and sz_stocks is not None and not sz_stocks.empty:
                # 合并沪深股票
                all_stocks = pd.concat([sh_stocks, sz_stocks], ignore_index=True)
                print(f"    📊 合并后总股票数: {len(all_stocks)}只")

                # 过滤掉ST、*ST等特殊股票
                filtered_stocks = all_stocks[
                    (~all_stocks['symbol'].str.contains('ST', na=False)) &
                    (~all_stocks['symbol'].str.contains('退', na=False)) &
                    (all_stocks['symbol'].str.len() >= 11)  # 确保是完整的股票代码格式
                ]

                SYMBOLS = list(filtered_stocks['symbol'])
                print(f"    ✅ 成功获取沪深两市A股: {len(SYMBOLS)}只")
                print(f"      沪市股票: {len([s for s in SYMBOLS if s.startswith('SHSE')])}只")
                print(f"      深市股票: {len([s for s in SYMBOLS if s.startswith('SZSE')])}只")
            else:
                raise Exception(f"股票数据获取失败 - 沪市: {sh_stocks is not None}, 深市: {sz_stocks is not None}")

        except Exception as e:
            print(f"    ❌ 获取全市场股票失败: {e}")
            # 使用扩展的备用股票池，包含更多知名股票
            print("    🔄 使用扩展备用股票池")
            SYMBOLS = [
                # 沪市主要股票
                'SHSE.600036', 'SHSE.601318', 'SHSE.600519', 'SHSE.600276', 'SHSE.600000',
                'SHSE.600887', 'SHSE.601166', 'SHSE.601288', 'SHSE.600585', 'SHSE.601398',
                'SHSE.601857', 'SHSE.601012', 'SHSE.600309', 'SHSE.601668', 'SHSE.600104',
                # 深市主要股票
                'SZSE.000002', 'SZSE.000001', 'SZSE.002415', 'SZSE.000858', 'SZSE.002594',
                'SZSE.000725', 'SZSE.002230', 'SZSE.000063', 'SZSE.002352', 'SZSE.000876',
                'SZSE.002142', 'SZSE.000568', 'SZSE.002475', 'SZSE.000895', 'SZSE.002304'
            ]
            print(f"    ⚠️ 使用扩展备用股票池: {len(SYMBOLS)}只 (包含沪深主要股票)")

        print(f"✅ 成功获取全市场股票池: {len(SYMBOLS)}只股票")
        print(f"  沪市股票: {len([s for s in SYMBOLS if s.startswith('SHSE')])}只")
        print(f"  深市股票: {len([s for s in SYMBOLS if s.startswith('SZSE')])}只")

        # 最终检查股票池是否有效
        if len(SYMBOLS) == 0:
            print("    ❌ 所有方法都无法获取股票池")
            # 使用最小核心股票池作为最后备选
            SYMBOLS = ['SHSE.600036', 'SHSE.601318', 'SHSE.600519', 'SZSE.000002', 'SZSE.000001']
            print(f"    🆘 使用最小核心股票池: {len(SYMBOLS)}只")

    except Exception as e:
        print(f"❌ 获取全市场股票池失败: {e}")
        # 使用最小核心股票池，确保策略能够运行
        print("🆘 使用最小核心股票池作为备选")
        SYMBOLS = ['SHSE.600036', 'SHSE.601318', 'SHSE.600519', 'SZSE.000002', 'SZSE.000001']
        print(f"⚠️ 备选股票池: {len(SYMBOLS)}只，策略将继续运行")
    print(f"选股条件:")
    print(f"  涨幅范围: {PRICE_CHANGE_MIN*100:.1f}% - {PRICE_CHANGE_MAX*100:.1f}%")
    print(f"  量比要求: >= {VOLUME_RATIO_MIN:.1f}倍")
    print(f"  换手率: {TURNOVER_MIN*100:.1f}% - {TURNOVER_MAX*100:.1f}%")
    print(f"  流通市值: {MARKET_CAP_MIN/10000:.0f}亿 - {MARKET_CAP_MAX/10000:.0f}亿")
    print(f"  均线条件: MA{MA_SHORT} >= MA{MA_MEDIUM} > MA{MA_LONG}")
    print(f"  RSI要求: > {RSI_MIN}")
    print(f"  多空力量: 买盘/卖盘 >= 1.2倍")
    print(f"止盈止损:")
    print(f"  固定止盈: +{FIXED_TAKE_PROFIT*100:.1f}%")
    print(f"  固定止损: -{FIXED_STOP_LOSS*100:.1f}%")
    print(f"  浮动止盈: -{FLOATING_TAKE_PROFIT*100:.1f}%")
    print(f"策略参数:")
    print(f"  股票池: {len(SYMBOLS)}只")
    print(f"  最大持仓: {MAX_POSITIONS}只")
    print(f"  单只仓位: {POSITION_SIZE_RATIO*100:.0f}%")
    print("="*70)

    # 初始化变量
    context.positions = {}        # 持仓记录
    context.selected_stocks = set()  # 已选中股票
    context.trade_log = []        # 交易日志记录
    context.error_count = 0       # 错误计数器
    context.selection_count = 0   # 选股统计
    context.daily_high = {}       # 记录当日最高价（用于浮动止损）
    context.selected_stocks_details = []  # 存储选中股票的详细信息

    # 数据管理策略：遵循掘金最佳实践
    context.subscribed_stocks = set()    # 已订阅的股票池

    # 财务数据缓存 - 按日缓存，避免重复获取
    context.financial_cache = {}  # 财务数据缓存
    context.cache_date = None     # 缓存日期，用于判断是否需要更新

    # 简化的状态管理
    context.daily_selection_sessions = {  # 每日选股会话记录
        'morning_done': False,    # 11:00选股完成标志
        'afternoon_done': False   # 14:00选股完成标志
    }

    # 选股统计 - 按天记录
    context.daily_stats = {}  # 每日选股统计 {date: {session: stats}}
    context.executed_orders = set()  # 已执行订单集合（防重复）

    # 全市场订阅策略：按需订阅，避免系统过载
    print("  📡 采用全市场按需订阅策略...")

    # 按需订阅模式 - 只订阅触发股票
    print("  🎯 按需订阅模式：只订阅触发股票，其他数据按需获取")
    context.strategy_state = 'SELECTING'

    # 使用定时任务驱动选股（参考示例代码）
    # 上午选股时间：11:00
    schedule(schedule_func=morning_stock_selection, date_rule='1d', time_rule='11:00:00')
    # 下午选股时间：14:00
    schedule(schedule_func=afternoon_stock_selection, date_rule='1d', time_rule='14:50:00')

    print(f"  📊 股票池规模: {len(SYMBOLS)}只")
    print(f"  🎯 全市场检查: {len(SYMBOLS)}只股票")
    print(f"  🎯 优化选股顺序: 财务→技术→涨幅")
    print(f"  📅 定时任务已设置:")
    print(f"    上午选股: 每日11:00")
    print(f"    下午选股: 每日14:00")

    # 财务数据缓存管理 - 遵循掘金最佳实践，避免重复获取
    today = context.now.strftime('%Y-%m-%d')

    # 检查是否需要更新缓存（每日更新一次）
    if context.cache_date != today:
        print("  🚀 开始批量预取财务数据...")

        # 判断运行模式
        is_backtest_mode = (context.mode == 2)  # 回测模式=2，实时模式=1

        from datetime import datetime, timedelta

        try:
            if is_backtest_mode:
                # 回测模式：直接获取当天数据
                print(f"  📅 回测模式 - 获取当天数据: {today}")
                context.financial_cache = batch_fetch_financial_data(context, SYMBOLS, today)
            else:
                # 实盘模式：使用"日期+1"方法获取当天数据，如果没有则回退
                tomorrow = (context.now + timedelta(days=1)).strftime('%Y-%m-%d')
                yesterday = (context.now - timedelta(days=1)).strftime('%Y-%m-%d')
                print(f"  📅 实盘模式 - 使用日期+1方法获取当天数据: {today} (查询日期: {tomorrow})")

                # 使用明天的日期查询（这样能获取到当天的数据）
                context.financial_cache = batch_fetch_financial_data(context, SYMBOLS, tomorrow)

                # 如果明天日期查询无数据，则尝试今天日期
                if not context.financial_cache or len(context.financial_cache.get('turnover_rate', {})) == 0:
                    print(f"  🔄 明天日期无数据，尝试今天日期: {today}")
                    context.financial_cache = batch_fetch_financial_data(context, SYMBOLS, today)

                    # 如果今天日期也无数据，则使用昨天日期
                    if not context.financial_cache or len(context.financial_cache.get('turnover_rate', {})) == 0:
                        print(f"  🔄 今天日期无数据，使用昨天日期: {yesterday}")
                        context.financial_cache = batch_fetch_financial_data(context, SYMBOLS, yesterday)

            # 更新缓存日期
            context.cache_date = today

            # 检查财务数据获取结果
            market_cap_count = len(context.financial_cache.get('market_cap', {}))
            turnover_count = len(context.financial_cache.get('turnover_rate', {}))

            if market_cap_count > 0 and turnover_count > 0:
                print("  ✅ 财务数据预取完成，启用每日全量筛选模式")
            else:
                print("  ⚠️ 财务数据预取不完整，将使用简化筛选模式")
                print(f"    市值数据: {market_cap_count}只, 换手率数据: {turnover_count}只")

        except Exception as e:
            print(f"  ❌ 财务数据预取失败: {e}")
            # 初始化空的财务缓存，避免后续访问出错
            context.financial_cache = {
                'market_cap': {},
                'turnover_rate': {},
                'fetch_time': today
            }
            context.cache_date = today
    else:
        print(f"  📋 使用已缓存的财务数据: {today}")
        print("  🔄 将使用实时数据获取模式")

# ==================== 定时任务模块 ====================
def morning_stock_selection(context):
    """
    上午选股定时任务 - 11:00执行
    """
    current_time = context.now.strftime('%H:%M:%S')
    current_date = context.now.date()

    print(f"\n[{context.now.strftime('%Y-%m-%d %H:%M:%S')}] 🌅 上午选股开始")
    print(f"  📅 日期: {current_date}")
    print(f"  📊 当前时间: {current_time}")
    print(f"  📈 当前持仓: {len(context.selected_stocks)}只，可选股: {MAX_POSITIONS - len(context.selected_stocks)}只")

    # 初始化每日统计（如果需要）
    date_str = current_date.strftime('%Y-%m-%d')
    if date_str not in context.daily_stats:
        context.daily_stats[date_str] = {
            'morning': {'checked': 0, 'candidates': 0, 'selected': 0, 'bought': 0},
            'afternoon': {'checked': 0, 'candidates': 0, 'selected': 0, 'bought': 0}
        }

    # 重置每日选股状态（如果是新的一天）
    if not hasattr(context, 'last_date') or context.last_date != current_date:
        context.last_date = current_date
        context.daily_selection_sessions = {
            'morning_done': False,
            'afternoon_done': False
        }

    # 检查是否还能选股
    if len(context.selected_stocks) < MAX_POSITIONS:
        # 检查是否已经执行过上午选股
        if not context.daily_selection_sessions.get('morning_done', False):
            execute_stock_selection(context, 'morning')
        else:
            print(f"  ⚠️ 上午选股已完成，跳过")
    else:
        print(f"  ❌ 持仓已满，跳过上午选股")

def afternoon_stock_selection(context):
    """
    下午选股定时任务 - 14:00执行
    """
    current_time = context.now.strftime('%H:%M:%S')
    current_date = context.now.date()

    print(f"\n[{context.now.strftime('%Y-%m-%d %H:%M:%S')}] 🌇 下午选股开始")
    print(f"  📅 日期: {current_date}")
    print(f"  📊 当前时间: {current_time}")
    print(f"  📈 当前持仓: {len(context.selected_stocks)}只，可选股: {MAX_POSITIONS - len(context.selected_stocks)}只")

    # 检查是否还能选股
    if len(context.selected_stocks) < MAX_POSITIONS:
        # 检查是否已经执行过下午选股
        if not context.daily_selection_sessions.get('afternoon_done', False):
            execute_stock_selection(context, 'afternoon')
        else:
            print(f"  ⚠️ 下午选股已完成，跳过")
    else:
        print(f"  ❌ 持仓已满，跳过下午选股")

# ==================== 事件处理模块 ====================
def on_tick(context, tick):
    """
    tick事件处理 - 借鉴雪球策略的事件驱动模式

    Args:
        context: 策略上下文
        tick: tick数据
    """
    try:
        # 简化版本：不再处理复杂的信号系统
        pass
    except Exception as e:
        print(f"tick事件处理异常: {e}")

def on_bar(context, bars):
    """K线数据处理 - 简化为仅处理止盈止损"""
    for bar in bars:
        if bar.frequency == '1d':
            handle_daily_selection(context, bar)  # 仅处理止盈止损

def handle_daily_selection(context, bar):
    """处理日线数据 - 日内选股逻辑和止盈止损"""
    symbol = bar.symbol
    current_price = bar.close

    # 每天重置选股记录（但保留持仓股票）
    current_date = bar.eob.date()
    if not hasattr(context, 'last_date') or context.last_date != current_date:
        # 保留当前持仓的股票，只重置选股状态
        current_positions = set(context.positions.keys()) if hasattr(context, 'positions') else set()
        context.selected_stocks = current_positions.copy()  # 保留持仓股票
        context.daily_high = {}
        context.last_date = current_date

        # 重置每日选股会话标志
        context.daily_selection_sessions = {
            'morning_done': False,    # 11:00选股完成标志
            'afternoon_done': False   # 14:00选股完成标志
        }

        # 简化版本：不再需要清理挂单

        # 清理过期的执行记录（每日重置，允许同一股票在不同日期重新选股）
        if hasattr(context, 'executed_orders'):
            context.executed_orders.clear()

        # 初始化当日统计
        date_str = current_date.strftime('%Y-%m-%d')
        context.daily_stats[date_str] = {
            'morning': {'candidates': 0, 'selected': 0, 'bought': 0},
            'afternoon': {'candidates': 0, 'selected': 0, 'bought': 0}
        }

        print(f"\n[{context.now}] 新交易日开始")
        print(f"  � 日期: {date_str}")
        print(f"  📊 当前时间: {context.now.strftime('%H:%M:%S')}")
        print(f"  📈 当前持仓: {len(current_positions)}只，可选股: {MAX_POSITIONS - len(current_positions)}只")
        print(f"  🎯 今日选股计划: 11:00 + 14:00 两次选股")

    # 更新当日最高价
    if symbol not in context.daily_high:
        context.daily_high[symbol] = current_price
    else:
        context.daily_high[symbol] = max(context.daily_high[symbol], current_price)

    # 1. 持仓股票的止盈止损检查（优先处理）
    if symbol in context.positions:
        position = context.positions[symbol]
        buy_price = position['price']
        profit_ratio = (current_price - buy_price) / buy_price
        daily_high = context.daily_high[symbol]

        # 固定止损
        if profit_ratio <= -FIXED_STOP_LOSS:
            sell_stock(context, symbol, current_price, position, "固定止损")
        # 固定止盈
        elif profit_ratio >= FIXED_TAKE_PROFIT:
            sell_stock(context, symbol, current_price, position, "固定止盈")
        # 浮动止盈（突破当日高点后回落3%）
        elif current_price < daily_high * (1 - FLOATING_TAKE_PROFIT) and daily_high > buy_price:
            sell_stock(context, symbol, current_price, position, "浮动止盈")
        # 破5日均线止损
        else:
            # 获取最新的5日均线（优化：使用context.data()利用订阅数据）
            try:
                hist_data = context.data(symbol=symbol, frequency='1d', count=MA_SHORT + 1)  # 只需要6天数据
                if hist_data is not None and not hist_data.empty and len(hist_data) >= MA_SHORT:
                    prices = hist_data['close'].tolist()
                    ma5 = calculate_ma(prices, MA_SHORT)
                    if ma5 and current_price < ma5:
                        sell_stock(context, symbol, current_price, position, "破5日均线")
            except Exception:
                # 如果订阅数据获取失败，降级使用history_n()
                hist_data = history_n(symbol=symbol, frequency='1d', count=MA_SHORT + 1, end_time=context.now, adjust=ADJUST_NONE, df=True)
                if hist_data is not None and not hist_data.empty and len(hist_data) >= MA_SHORT:
                    prices = hist_data['close'].tolist()
                    ma5 = calculate_ma(prices, MA_SHORT)
                    if ma5 and current_price < ma5:
                        sell_stock(context, symbol, current_price, position, "破5日均线")

    # 2. 智能选股逻辑（支持实时和回测模式）
    current_time = context.now.strftime("%H:%M:%S")
    current_hour = int(current_time.split(':')[0])
    current_minute = int(current_time.split(':')[1])

    # 判断运行模式（实时 vs 回测）- 使用掘金SDK标准方式
    is_backtest_mode = (context.mode == 2)  # 回测模式=2，实时模式=1

    # 确定选股会话和触发条件
    session_type = None
    session_done_key = None
    should_execute = False

    # 回测模式选股触发：每日收盘时执行选股（修复触发条件）
    if is_backtest_mode:
        # 调试：显示触发条件检查
        if symbol == SYMBOLS[0]:
            # 检查是否是新的交易日
            current_date = context.now.date()
            if not hasattr(context, 'last_date') or context.last_date != current_date:
                # 新交易日，重置选股会话状态
                context.last_date = current_date
                context.daily_selection_sessions = {
                    'morning_done': False,
                    'afternoon_done': False
                }

                # 初始化每日统计
                date_str = current_date.strftime('%Y-%m-%d')
                if date_str not in context.daily_stats:
                    context.daily_stats[date_str] = {
                        'morning': {'checked': 0, 'candidates': 0, 'selected': 0, 'bought': 0},
                        'afternoon': {'checked': 0, 'candidates': 0, 'selected': 0, 'bought': 0}
                    }

                print(f"  🔍 回测模式选股检查:")
                print(f"    运行模式: 回测模式 (context.mode={context.mode})")
                print(f"    当前时间: {current_time}")
                print(f"    交易日期: {current_date}")
                print(f"    触发股票: {symbol}")
                print(f"    执行计划: 模拟11:00和14:00选股")

                # 显示当前仓位详情
                if hasattr(context, 'positions') and context.positions:
                    print(f"    当前持仓: {list(context.positions.keys())}")

                # 检查是否还能选股
                position_condition = len(context.selected_stocks) < MAX_POSITIONS
                print(f"    持仓条件: {position_condition} (当前{len(context.selected_stocks)}/{MAX_POSITIONS})")

                if position_condition:
                    # 回测模式：每日执行上午和下午选股
                    print(f"    ✅ 开始执行选股...")
                    if not context.daily_selection_sessions.get('morning_done', False):
                        execute_stock_selection(context, 'morning')

                    if not context.daily_selection_sessions.get('afternoon_done', False):
                        execute_stock_selection(context, 'afternoon')
                else:
                    print(f"    ❌ 持仓已满，跳过选股")

    else:
        # 实时模式：按时间窗口执行选股
        # 上午选股时间：11:00-11:30
        if current_hour == 11 and 0 <= current_minute <= 30:
            session_type = 'morning'
            session_done_key = 'morning_done'
            should_execute = True
        # 下午选股时间：14:00-14:30
        elif current_hour == 14 and 0 <= current_minute <= 30:
            session_type = 'afternoon'
            session_done_key = 'afternoon_done'
            should_execute = True

        # 检查选股条件
        if should_execute and symbol == SYMBOLS[0]:  # 只在第一只股票时执行
            session_condition = not context.daily_selection_sessions.get(session_done_key, False)
            position_condition = len(context.selected_stocks) < MAX_POSITIONS

            print(f"  🔍 实时模式选股检查:")
            print(f"    运行模式: 实时模式 (context.mode={context.mode})")
            print(f"    当前时间: {current_time}")
            print(f"    选股会话: {session_type}")
            print(f"    会话状态: {session_condition} (done={context.daily_selection_sessions.get(session_done_key, False)})")
            print(f"    持仓条件: {position_condition} (当前{len(context.selected_stocks)}/{MAX_POSITIONS})")

            # 显示当前仓位详情
            if hasattr(context, 'positions') and context.positions:
                print(f"    当前持仓: {list(context.positions.keys())}")

            # 执行选股
            if session_condition and position_condition:
                execute_stock_selection(context, session_type)

def execute_stock_selection(context, session_type):
    """执行选股逻辑 - 从handle_daily_selection中提取出来"""
    print(f"🕐 {session_type}选股时间，开始漏斗式筛选...")

    # 获取当日统计对象
    date_str = context.last_date.strftime('%Y-%m-%d')
    session_stats = context.daily_stats[date_str][session_type]

    # 每日全量筛选候选股票（涨幅条件）
    candidate_symbols = []
    total_checked = 0
    valid_data_count = 0

    # 筛选通过率统计
    filter_stats = {
        'market_cap_passed': 0,
        'turnover_passed': 0,
        'ma_passed': 0,
        'rsi_passed': 0,
        'volume_ratio_passed': 0,
        'price_change_passed': 0
    }

    # 全市场股票检查
    check_count = len(SYMBOLS)  # 检查全市场所有股票
    print(f"  📊 全市场筛选候选股票 (检查范围: {check_count}只)")

    # 检查是否需要继续选股
    if len(context.selected_stocks) >= MAX_POSITIONS:
        print(f"  ⚠️ 已达最大持仓数量，跳过{session_type}选股")
        context.daily_selection_sessions[f'{session_type}_done'] = True
        return

    # 优化筛选顺序：先用快速条件筛选，最后用涨幅条件
    for check_symbol in SYMBOLS[:check_count]:
        if check_symbol in context.selected_stocks:
            continue  # 跳过已选中的股票

        total_checked += 1
        try:
            # 第一步：快速财务筛选（使用缓存数据）
            market_cap = get_cached_financial_data(context, check_symbol, 'market_cap')
            # 调试：打印市值信息
            if market_cap is not None:
                print(f"    💰 {check_symbol} 市值: {market_cap:.0f}万元 ({market_cap/10000:.1f}亿) [范围: {MARKET_CAP_MIN/10000:.0f}-{MARKET_CAP_MAX/10000:.0f}亿]")
            else:
                print(f"    ❌ {check_symbol} 市值数据缺失")

            if market_cap is None or not (MARKET_CAP_MIN <= market_cap <= MARKET_CAP_MAX):
                if market_cap is not None:
                    print(f"    ❌ {check_symbol} 市值不符合条件: {market_cap/10000:.1f}亿 (要求: {MARKET_CAP_MIN/10000:.0f}-{MARKET_CAP_MAX/10000:.0f}亿)")
                continue
            filter_stats['market_cap_passed'] += 1

            turnover_rate = get_cached_financial_data(context, check_symbol, 'turnover_rate')
            # 打印换手率信息用于调试
            if turnover_rate is not None:
                print(f"    📊 {check_symbol} 换手率: {turnover_rate:.3f} ({turnover_rate*100:.2f}%) [范围: {TURNOVER_MIN*100:.1f}%-{TURNOVER_MAX*100:.1f}%]")
            else:
                print(f"    ❌ {check_symbol} 换手率数据缺失")

            if turnover_rate is None or not (TURNOVER_MIN <= turnover_rate <= TURNOVER_MAX):
                if turnover_rate is not None:
                    print(f"    ❌ {check_symbol} 换手率不符合条件: {turnover_rate*100:.2f}% (要求: {TURNOVER_MIN*100:.1f}%-{TURNOVER_MAX*100:.1f}%)")
                continue
            filter_stats['turnover_passed'] += 1

            # 第二步：获取历史数据进行技术指标筛选（优化：减少API调用）
            try:
                # 优先使用订阅数据，如果没有则使用history_n
                hist_data = None
                try:
                    if check_symbol in context.subscribed_stocks:
                        hist_data = context.data(symbol=check_symbol, frequency='1d', count=25)
                except:
                    pass

                # 如果订阅数据获取失败，使用history_n作为备选
                if hist_data is None or hist_data.empty:
                    current_time_str = context.now.strftime('%Y-%m-%d %H:%M:%S')
                    hist_data = history_n(
                        symbol=check_symbol,
                        frequency='1d',
                        count=25,
                        end_time=current_time_str,
                        fields='open,close,high,low,volume',
                        adjust=ADJUST_NONE,
                        df=True
                    )
                if hist_data is None or hist_data.empty or len(hist_data) < MA_LONG:
                    continue
            except Exception as e:
                continue

            prices = hist_data['close'].tolist()
            volumes = hist_data['volume'].tolist()

            # 均线条件检查
            ma5 = calculate_ma(prices, MA_SHORT)
            ma10 = calculate_ma(prices, MA_MEDIUM)
            ma20 = calculate_ma(prices, MA_LONG)

            if ma5 is not None and ma10 is not None and ma20 is not None:
                print(f"    📈 {check_symbol} 均线: MA5={ma5:.2f}, MA10={ma10:.2f}, MA20={ma20:.2f}")
                print(f"      条件检查: MA5>=MA10? {ma5>=ma10}, MA10>MA20? {ma10>ma20}")
            else:
                print(f"    ❌ {check_symbol} 均线数据不足")

            if ma5 is None or ma10 is None or ma20 is None or not (ma5 >= ma10 and ma10 > ma20):
                if ma5 is not None and ma10 is not None and ma20 is not None:
                    print(f"    ❌ {check_symbol} 均线条件不符合: MA5({ma5:.2f}) >= MA10({ma10:.2f}) > MA20({ma20:.2f})")
                continue
            filter_stats['ma_passed'] += 1

            # RSI条件检查
            rsi = calculate_rsi(prices)
            if rsi is not None:
                print(f"    📈 {check_symbol} RSI: {rsi:.1f} [要求: >{RSI_MIN}]")
            else:
                print(f"    ❌ {check_symbol} RSI数据不足")
            if rsi is None or rsi <= RSI_MIN:
                if rsi is not None:
                    print(f"    ❌ {check_symbol} RSI不符合条件: {rsi:.1f} (要求: >{RSI_MIN})")
                continue
            filter_stats['rsi_passed'] += 1

            # 量比条件检查（修改为近5日平均）
            if len(volumes) >= 6:
                current_volume = volumes[-1]
                avg_volume = sum(volumes[-6:-1]) / 5  # 近5日平均成交量
                volume_ratio = calculate_volume_ratio(current_volume, avg_volume)
                print(f"    📊 {check_symbol} 量比: {volume_ratio:.2f}倍 [要求: >={VOLUME_RATIO_MIN:.1f}倍]")
                print(f"      当日成交量: {current_volume:,.0f}, 近5日平均: {avg_volume:,.0f}")
                if volume_ratio < VOLUME_RATIO_MIN:
                    print(f"    ❌ {check_symbol} 量比不符合条件: {volume_ratio:.2f}倍 (要求: >={VOLUME_RATIO_MIN:.1f}倍)")
                    continue
                filter_stats['volume_ratio_passed'] += 1
            else:
                print(f"    ❌ {check_symbol} 成交量数据不足(需要6天数据)")
                continue

            # 第三步：最后检查涨幅条件（相对昨日收盘价）
            try:
                # 获取历史数据（至少需要2天数据：昨日和今日）
                hist_data = history_n(
                    symbol=check_symbol,
                    frequency='1d',
                    count=2,
                    end_time=context.now,
                    adjust=ADJUST_NONE,
                    df=False
                )
                if not hist_data or len(hist_data) < 2:
                    continue

                # 获取昨日收盘价
                yesterday_close = hist_data[-2]['close']  # 昨日收盘价

                # 获取当前价格
                if context.mode == 1:  # 实时模式
                    try:
                        # 实时模式：获取真正的实时价格
                        current_tick = current([check_symbol])
                        if current_tick and len(current_tick) > 0:
                            current_price = current_tick[0]['price']  # 实时价格
                        else:
                            # 如果无法获取实时价格，跳过该股票
                            continue
                    except Exception:
                        # current()函数调用失败，跳过该股票
                        continue
                else:  # 回测模式
                    # 回测模式：使用当日收盘价（相当于全天涨跌幅）
                    current_price = hist_data[-1]['close']

                # 数据有效性检查
                if yesterday_close <= 0 or current_price <= 0:
                    continue

                valid_data_count += 1

                # 检查涨幅条件（相对昨日收盘价的涨幅）
                price_change = (current_price - yesterday_close) / yesterday_close
                if PRICE_CHANGE_MIN <= price_change <= PRICE_CHANGE_MAX:
                    filter_stats['price_change_passed'] += 1
                    candidate_symbols.append(check_symbol)
                    # 获取换手率信息用于显示
                    turnover_display = get_cached_financial_data(context, check_symbol, 'turnover_rate')
                    turnover_str = f"换手率:{turnover_display*100:.2f}%" if turnover_display else "换手率:N/A"
                    print(f"    ✅ 符合涨幅条件: {check_symbol} 涨幅{price_change*100:.2f}% (昨收:{yesterday_close:.2f} 现价:{current_price:.2f}) {turnover_str}")
                else:
                    print(f"    ❌ {check_symbol} 涨幅不符合条件: {price_change*100:.2f}% (要求: {PRICE_CHANGE_MIN*100:.1f}%-{PRICE_CHANGE_MAX*100:.1f}%)")

            except Exception as e:
                # 数据获取异常时跳过该股票
                continue

        except Exception:
            continue

    # 数据获取状态诊断（移到循环外部）
    print(f"  📊 数据获取状态诊断:")
    print(f"    检查股票总数: {total_checked}只")
    print(f"    有效数据股票: {valid_data_count}只")
    print(f"    数据获取成功率: {valid_data_count/max(1,total_checked)*100:.1f}%")
    print(f"    涨幅符合条件: {len(candidate_symbols)}只")

    # 筛选通过率统计
    print(f"  📈 筛选条件通过率统计:")
    print(f"    💰 市值筛选: {filter_stats['market_cap_passed']}/{total_checked} ({filter_stats['market_cap_passed']/max(1,total_checked)*100:.1f}%)")
    print(f"    🔄 换手率筛选: {filter_stats['turnover_passed']}/{filter_stats['market_cap_passed']} ({filter_stats['turnover_passed']/max(1,filter_stats['market_cap_passed'])*100:.1f}%)")
    print(f"    📊 均线筛选: {filter_stats['ma_passed']}/{filter_stats['turnover_passed']} ({filter_stats['ma_passed']/max(1,filter_stats['turnover_passed'])*100:.1f}%)")
    print(f"    📈 RSI筛选: {filter_stats['rsi_passed']}/{filter_stats['ma_passed']} ({filter_stats['rsi_passed']/max(1,filter_stats['ma_passed'])*100:.1f}%)")
    print(f"    📊 量比筛选: {filter_stats['volume_ratio_passed']}/{filter_stats['rsi_passed']} ({filter_stats['volume_ratio_passed']/max(1,filter_stats['rsi_passed'])*100:.1f}%)")
    print(f"    🎯 涨幅筛选: {filter_stats['price_change_passed']}/{filter_stats['volume_ratio_passed']} ({filter_stats['price_change_passed']/max(1,filter_stats['volume_ratio_passed'])*100:.1f}%)")
    print(f"    🏆 总体通过率: {len(candidate_symbols)}/{total_checked} ({len(candidate_symbols)/max(1,total_checked)*100:.1f}%)")

    if valid_data_count == 0:
        print(f"  ⚠️ 无任何股票获取到有效数据，可能的原因:")
        print(f"    1. 网络连接问题")
        print(f"    2. 数据订阅失败")
        print(f"    3. 交易日期问题")
        print(f"    4. API权限问题")

    # 更新候选股票统计
    session_stats['candidates'] = len(candidate_symbols)

    if candidate_symbols:
        print(f"  📊 候选股票池: {len(candidate_symbols)}只 (涨幅{PRICE_CHANGE_MIN*100:.1f}%-{PRICE_CHANGE_MAX*100:.1f}%)")

        # 完整的统一筛选（每日全量筛选）
        selected_symbols = unified_stock_selection(context, candidate_symbols)

        # 更新选中股票统计
        session_stats['selected'] = len(selected_symbols)

        # 直接买入（学习示例代码的简化模式）
        bought_count = 0
        for selected_symbol in selected_symbols:
            if (len(context.selected_stocks) < MAX_POSITIONS and
                selected_symbol not in context.selected_stocks):

                try:
                    # 获取当前价格（使用history_n()获取收盘价）
                    current_data = history_n(symbol=selected_symbol, frequency='1d', count=1, end_time=context.now, df=False)
                    if current_data and len(current_data) > 0:
                        current_price = current_data[0]['close']

                        # 直接买入（无信号系统）
                        success = buy_stock(context, selected_symbol, current_price)
                        if success:
                            context.selected_stocks.add(selected_symbol)
                            bought_count += 1
                            print(f"    ✅ 直接买入成功: {selected_symbol} @ {current_price:.2f} ({session_type})")

                            # 记录选股详情（获取完整指标数据）
                            try:
                                # 获取选股时的指标数据（修复：使用history_n()无需订阅，添加end_time获取最新数据）
                                hist_data = history_n(symbol=selected_symbol, frequency='1d', count=25, end_time=context.now, adjust=ADJUST_NONE, df=True)
                                if hist_data is not None and not hist_data.empty:
                                    prices = hist_data['close'].tolist()
                                    volumes = hist_data['volume'].tolist()

                                    # 计算涨幅（相对昨日收盘价）
                                    if len(prices) >= 2:
                                        yesterday_close = prices[-2]  # 昨日收盘价

                                        # 获取当前价格
                                        if context.mode == 1:  # 实时模式
                                            try:
                                                current_tick = current([selected_symbol])
                                                if current_tick and len(current_tick) > 0:
                                                    current_price = current_tick[0]['price']  # 实时价格
                                                else:
                                                    current_price = prices[-1]  # 备用：最新收盘价
                                            except:
                                                current_price = prices[-1]  # 备用：最新收盘价
                                        else:  # 回测模式
                                            current_price = prices[-1]  # 回测模式使用当日收盘价

                                        price_change = (current_price - yesterday_close) / yesterday_close if yesterday_close > 0 else 0
                                    else:
                                        price_change = 0
                                        current_price = prices[-1] if prices else 0

                                    # 计算量比（修改为近5日平均）
                                    if len(volumes) >= 6:
                                        current_volume = volumes[-1]
                                        avg_volume = sum(volumes[-6:-1]) / 5  # 近5日平均成交量
                                        volume_ratio = calculate_volume_ratio(current_volume, avg_volume)
                                    else:
                                        volume_ratio = 0

                                    # 计算RSI
                                    rsi = calculate_rsi(prices, 14) if len(prices) >= 14 else 0

                                    # 计算均线
                                    ma5 = calculate_ma(prices, 5) if len(prices) >= 5 else 0
                                    ma10 = calculate_ma(prices, 10) if len(prices) >= 10 else 0
                                    ma20 = calculate_ma(prices, 20) if len(prices) >= 20 else 0

                                    # 获取市值和换手率
                                    market_cap = get_cached_financial_data(context, selected_symbol, 'market_cap') or 0
                                    turnover_rate = get_cached_financial_data(context, selected_symbol, 'turnover_rate') or 0

                                    stock_detail = {
                                        'symbol': selected_symbol,
                                        'selection_date': context.now.strftime('%Y-%m-%d'),
                                        'selection_time': context.now.strftime('%H:%M:%S'),
                                        'price': current_price,
                                        'price_change': price_change,
                                        'volume_ratio': volume_ratio if volume_ratio else 0,
                                        'rsi': rsi if rsi else 0,
                                        'ma5': ma5 if ma5 else 0,
                                        'ma10': ma10 if ma10 else 0,
                                        'ma20': ma20 if ma20 else 0,
                                        'market_cap': market_cap,
                                        'turnover_rate': turnover_rate,
                                        'selection_method': 'batch_funnel_filter'
                                    }
                                else:
                                    # 数据不足时的备用记录
                                    market_cap = get_cached_financial_data(context, selected_symbol, 'market_cap') or 0
                                    turnover_rate = get_cached_financial_data(context, selected_symbol, 'turnover_rate') or 0

                                    stock_detail = {
                                        'symbol': selected_symbol,
                                        'selection_date': context.now.strftime('%Y-%m-%d'),
                                        'selection_time': context.now.strftime('%H:%M:%S'),
                                        'price': current_price,
                                        'price_change': 0,
                                        'volume_ratio': 0,
                                        'rsi': 0,
                                        'ma5': 0,
                                        'ma10': 0,
                                        'ma20': 0,
                                        'market_cap': market_cap,
                                        'turnover_rate': turnover_rate,
                                        'selection_method': 'batch_funnel_filter'
                                    }
                                context.selected_stocks_details.append(stock_detail)
                            except Exception as e:
                                print(f"    ⚠️ 记录选股详情异常: {e}")
                        else:
                            print(f"    ❌ 买入失败: {selected_symbol} @ {current_price:.2f}")

                except Exception as e:
                    print(f"    ❌ 买入处理失败: {selected_symbol} - {e}")
                    context.error_count += 1

            # 更新买入统计
            session_stats['bought'] = bought_count

            # 选股完成后切换到监控模式
            if len(context.selected_stocks) >= MAX_POSITIONS:
                switch_to_minimal_subscription(context, list(context.selected_stocks))
                print("  🎯 达到最大持仓，切换到监控模式")

    # 标记当前会话完成
    session_done_key = f'{session_type}_done'
    context.daily_selection_sessions[session_done_key] = True

    # 更新选股计数
    context.selection_count += 1

    # 显示本次选股统计
    print(f"  ✅ {session_type}选股完成")
    print(f"    📊 候选股票: {session_stats['candidates']}只")
    print(f"    🎯 筛选通过: {session_stats['selected']}只")
    print(f"    💰 直接买入: {session_stats['bought']}只")

    # 显示当前仓位详情
    display_current_positions(context)

    # 显示每日累计统计
    display_daily_summary(context, date_str)

    # 显示当日选股详情
    display_daily_selection_details(context, date_str)

def display_daily_summary(context, date_str):
    """显示每日选股统计汇总"""
    if date_str not in context.daily_stats:
        return

    stats = context.daily_stats[date_str]
    morning = stats['morning']
    afternoon = stats['afternoon']

    print(f"\n  📈 {date_str} 每日选股汇总:")
    print(f"    🌅 上午(11:00): 候选{morning['candidates']}只 → 筛选{morning['selected']}只 → 买入{morning['bought']}只")
    print(f"    🌆 下午(14:00): 候选{afternoon['candidates']}只 → 筛选{afternoon['selected']}只 → 买入{afternoon['bought']}只")
    print(f"    📊 全天合计: 候选{morning['candidates']+afternoon['candidates']}只 → 筛选{morning['selected']+afternoon['selected']}只 → 买入{morning['bought']+afternoon['bought']}只")

def display_daily_selection_details(context, date_str):
    """显示当日选股详情和指标数值"""
    if not hasattr(context, 'selected_stocks_details'):
        return

    # 筛选当日的选股记录
    daily_selections = [stock for stock in context.selected_stocks_details
                       if stock['selection_date'] == date_str]

    if not daily_selections:
        return

    print(f"\n  📋 {date_str} 当日选股详情:")
    print("  " + "="*80)
    print(f"  {'股票代码':<12} {'市值(亿)':<8} {'换手率':<8} {'MA5':<8} {'MA10':<8} {'MA20':<8} {'RSI':<6} {'量比':<6} {'涨幅':<8}")
    print("  " + "-"*80)

    for stock in daily_selections:
        symbol = stock['symbol']
        market_cap = stock['market_cap'] / 10000  # 转换为亿元
        turnover = stock['turnover_rate'] * 100   # 转换为百分比
        ma5 = stock.get('ma5', 0)
        ma10 = stock.get('ma10', 0)
        ma20 = stock.get('ma20', 0)
        rsi = stock.get('rsi', 0)
        volume_ratio = stock.get('volume_ratio', 0)
        price_change = stock.get('price_change', stock.get('intraday_change', 0)) * 100  # 转换为百分比，兼容新旧字段名

        print(f"  {symbol:<12} {market_cap:<8.1f} {turnover:<8.2f} {ma5:<8.2f} {ma10:<8.2f} {ma20:<8.2f} {rsi:<6.1f} {volume_ratio:<6.2f} {price_change:<8.2f}")

    print("  " + "="*80)
    print(f"  📊 当日共选中 {len(daily_selections)} 只股票")

    # 显示指标统计
    if daily_selections:
        avg_market_cap = sum(s['market_cap'] for s in daily_selections) / len(daily_selections) / 10000
        avg_turnover = sum(s['turnover_rate'] for s in daily_selections) / len(daily_selections) * 100
        avg_rsi = sum(s.get('rsi', 0) for s in daily_selections) / len(daily_selections)
        avg_volume_ratio = sum(s.get('volume_ratio', 0) for s in daily_selections) / len(daily_selections)
        avg_price_change = sum(s.get('price_change', s.get('intraday_change', 0)) for s in daily_selections) / len(daily_selections) * 100

        print(f"  📈 平均指标: 市值{avg_market_cap:.1f}亿 换手率{avg_turnover:.2f}% RSI{avg_rsi:.1f} 量比{avg_volume_ratio:.2f} 涨幅{avg_price_change:.2f}%")

def display_current_positions(context):
    """显示当前仓位详情"""
    if not hasattr(context, 'positions') or not context.positions:
        print("    💼 当前无持仓")
        return

    print(f"    💼 当前持仓 ({len(context.positions)}/{MAX_POSITIONS}):")
    total_amount = 0
    for symbol, pos in context.positions.items():
        amount = pos['amount']
        total_amount += amount
        print(f"      {symbol}: {pos['volume']}股 @ {pos['price']:.2f}元 = {amount:.2f}元 ({pos['buy_time']})")

    print(f"    💰 持仓总金额: {total_amount:.2f}元")

    # 显示可用资金
    try:
        account_cash = context.account().cash
        available_cash = account_cash['available']
        print(f"    💵 可用资金: {available_cash:.2f}元")
    except:
        print(f"    💵 可用资金: 查询失败")

def dynamic_subscribe(context, new_symbols):
    """
    动态订阅新股票 - 遵循掘金最佳实践，简化订阅逻辑

    Args:
        context: 策略上下文
        new_symbols: 需要新订阅的股票列表
    """
    if not new_symbols:
        return

    # 过滤出未订阅的股票
    unsubscribed = [symbol for symbol in new_symbols if symbol not in context.subscribed_stocks]

    if unsubscribed:
        try:
            # 统一使用适中的历史数据量，避免频繁变更订阅参数
            count = MA_LONG + 5  # 足够计算技术指标的数据量

            subscribe(symbols=unsubscribed, frequency='1d', count=count, unsubscribe_previous=False)
            context.subscribed_stocks.update(unsubscribed)
            print(f"  📡 订阅新股票: {len(unsubscribed)}只 (count={count})")

        except Exception as e:
            print(f"  ❌ 订阅失败: {e}")
            # 订阅失败时，仍然标记为已订阅，避免重复尝试
            context.subscribed_stocks.update(unsubscribed)

def switch_to_minimal_subscription(context, selected_symbols):
    """
    切换到监控模式订阅 - 遵循掘金最佳实践

    Args:
        context: 策略上下文
        selected_symbols: 已选中的股票列表
    """
    if context.strategy_state != 'MONITORING':
        context.strategy_state = 'MONITORING'

        # 订阅选中的股票，使用足够的数据支持技术指标计算
        if selected_symbols:
            try:
                subscribe(symbols=selected_symbols, frequency='1d', count=MA_LONG + 5, unsubscribe_previous=True)
                context.subscribed_stocks = set(selected_symbols)
                print(f"  🔄 切换到监控模式: {len(selected_symbols)}只股票 (count={MA_LONG + 5})")
            except Exception as e:
                print(f"  ❌ 监控模式订阅失败: {e}")

# ==================== 统一筛选模块 ====================
def unified_stock_selection(context, symbols):
    """
    简化选股函数 - 候选股票已经过预筛选，这里只做最终确认

    Args:
        context: 策略上下文
        symbols: 预筛选后的候选股票池

    Returns:
        list: 最终选中的股票列表
    """
    print(f"🔍 最终确认筛选: 候选股票池 {len(symbols)}只")

    selected_symbols = []

    for symbol in symbols:
        if symbol in context.selected_stocks:
            continue  # 跳过已选中的股票

        try:
            # 多空力量对比筛选（暂时跳过，保留框架）
            # TODO: 实现真实盘口数据获取
            bid_ask_ratio = 1.5  # 模拟值，实际应该从盘口数据计算
            if bid_ask_ratio >= 1.2:
                selected_symbols.append(symbol)

        except Exception:
            # 异常时也通过，避免因实时数据问题影响选股
            selected_symbols.append(symbol)

    print(f"🎯 最终确认完成: 选中 {len(selected_symbols)}只股票")
    if selected_symbols:
        print(f"  📋 选中股票: {selected_symbols}")

    return selected_symbols



# ==================== 交易函数（基于模板） ====================
def buy_stock(context, symbol, price):
    """买入股票 - 基于模板的改进版"""
    try:
        # 查询账户资金
        account_cash = context.account().cash
        available_cash = account_cash['available']

        # 计算买入股数
        target_amount = min(available_cash * POSITION_SIZE_RATIO, available_cash)
        volume = int(target_amount / price / 100) * 100

        # 科创板特殊处理：最小200股
        if symbol[:7] == 'SHSE.68':
            volume = min(200, volume) if volume > 0 else 200
        elif volume < 100:
            volume = 100

        # 检查资金是否足够
        required_amount = volume * price
        if required_amount > available_cash:
            print(f"[{context.now}] 资金不足: {symbol} 需要{required_amount:.2f}元，可用{available_cash:.2f}元")
            return False

        # 限价下单（避免市价单的保护限价问题）
        print(f"[{context.now}] 准备下单: {symbol} 买入{volume}股 @ {price:.2f}元")
        order = order_volume(symbol=symbol, volume=volume, side=OrderSide_Buy,
                           order_type=OrderType_Limit, position_effect=PositionEffect_Open, price=price)

        if order and len(order) > 0:
            actual_amount = volume * price
            context.positions[symbol] = {
                'price': price,
                'volume': volume,
                'amount': actual_amount,
                'buy_time': context.now.strftime('%H:%M:%S')
            }
            print(f"[{context.now}] 买入成功: {symbol} {volume}股 @ {price:.2f}元")

            # 立即订阅新买入的股票，支持后续止盈止损监控
            try:
                subscribe(symbols=[symbol], frequency='1d', count=MA_SHORT + 1, unsubscribe_previous=False)
                context.subscribed_stocks.add(symbol)
                print(f"  📡 已订阅新持仓股票: {symbol}")
            except Exception as e:
                print(f"  ⚠️ 订阅新持仓股票失败: {symbol} - {e}")

            # 添加交易日志
            add_trade_log(context, symbol, "买入", price, volume)
            return True
        else:
            print(f"[{context.now}] 买入失败: {symbol} - 下单返回空结果")
            if order:
                print(f"  下单结果: {order}")
            context.error_count += 1
            return False

    except Exception as e:
        print(f"[{context.now}] 买入异常: {symbol} {str(e)}")
        context.error_count += 1
        return False

def sell_stock(context, symbol, price, position, reason):
    """卖出股票 - 基于模板的改进版"""
    try:
        # 查询实际持仓
        account_position = context.account().position(symbol=symbol, side=PositionSide_Long)
        if not account_position:
            return

        # 计算可卖数量（排除今日买入的股票）
        available_volume = account_position['available'] - account_position['volume_today']
        sell_volume = min(position['volume'], available_volume)

        if sell_volume <= 0:
            return

        buy_price = position['price']

        # 限价下单（避免市价单的保护限价问题）
        print(f"[{context.now}] 准备卖出: {symbol} 卖出{sell_volume}股 @ {price:.2f}元")
        order = order_volume(symbol=symbol, volume=sell_volume, side=OrderSide_Sell,
                           order_type=OrderType_Limit, position_effect=PositionEffect_Close, price=price)

        if order and len(order) > 0:
            profit = (price - buy_price) * sell_volume
            profit_ratio = (price - buy_price) / buy_price * 100

            # 如果全部卖出，删除持仓记录
            if sell_volume >= position['volume']:
                del context.positions[symbol]
                # 从选中股票中移除
                context.selected_stocks.discard(symbol)
                # 取消订阅已卖出的股票，释放资源
                try:
                    unsubscribe(symbols=[symbol], frequency='1d')
                    context.subscribed_stocks.discard(symbol)
                    print(f"  📡 已取消订阅卖出股票: {symbol}")
                except Exception as e:
                    print(f"  ⚠️ 取消订阅失败: {symbol} - {e}")
            else:
                # 部分卖出，更新持仓记录
                context.positions[symbol]['volume'] -= sell_volume
                context.positions[symbol]['amount'] = context.positions[symbol]['volume'] * buy_price

            print(f"[{context.now}] {reason}: {symbol} {sell_volume}股 @ {price:.2f}元 "
                  f"盈亏:{profit:.0f}元 ({profit_ratio:+.2f}%)")

            # 添加交易日志
            add_trade_log(context, symbol, "卖出", price, sell_volume, profit, profit_ratio, reason)
        else:
            print(f"[{context.now}] 卖出失败: {symbol}")
            context.error_count += 1

    except Exception as e:
        print(f"[{context.now}] 卖出异常: {symbol} {str(e)}")
        context.error_count += 1

def on_order_status(context, order):
    """订单状态处理 - 基于模板的简化版"""
    try:
        if order.status == 8:  # 订单被拒绝
            context.error_count += 1
            print(f"[{context.now}] 订单拒绝: {order.symbol}")

        elif order.status == 3:  # 订单完全成交
            side_text = "买入" if order.position_effect == PositionEffect_Open else "卖出"
            print(f"[{context.now}] 成交: {side_text}{order.symbol} {order.filled_volume}股 @ {order.filled_vwap:.2f}元")

    except Exception:
        context.error_count += 1

def on_execution_report(context, execrpt):
    """委托执行回报事件处理"""
    try:
        symbol = execrpt.symbol
        side = "买入" if execrpt.side == OrderSide_Buy else "卖出"
        volume = execrpt.volume
        price = execrpt.price
        amount = execrpt.amount

        print(f"[{context.now}] 委托执行回报: {side} {symbol} {volume}股 @ {price:.2f}元，金额: {amount:.2f}元")

        # 记录执行详情到交易日志
        execution_info = {
            'time': context.now,
            'symbol': symbol,
            'side': side,
            'volume': volume,
            'price': price,
            'amount': amount,
            'exec_id': execrpt.exec_id if hasattr(execrpt, 'exec_id') else 'N/A'
        }

        if not hasattr(context, 'execution_log'):
            context.execution_log = []
        context.execution_log.append(execution_info)

    except Exception as e:
        print(f"[{context.now}] 委托执行回报处理异常: {e}")
        context.error_count += 1

def on_account_status(context, account_status):
    """交易账户状态变化事件处理"""
    try:
        account_id = account_status.account_id
        status = account_status.status

        # 账户状态映射
        status_map = {
            1: "正常",
            2: "资金不足",
            3: "持仓不足",
            4: "委托被拒绝",
            5: "连接断开",
            6: "未知错误"
        }

        status_desc = status_map.get(status, f"未知状态({status})")
        print(f"[{context.now}] 账户状态变化: {account_id} - {status_desc}")

        # 记录账户状态变化
        if not hasattr(context, 'account_status_log'):
            context.account_status_log = []

        status_info = {
            'time': context.now,
            'account_id': account_id,
            'status': status,
            'status_desc': status_desc
        }
        context.account_status_log.append(status_info)

        # 如果账户状态异常，增加错误计数
        if status != 1:  # 非正常状态
            context.error_count += 1

    except Exception as e:
        print(f"[{context.now}] 账户状态处理异常: {e}")
        context.error_count += 1

def on_error(context, code, info):
    """错误事件处理"""
    try:
        print(f"[{context.now}] 系统错误: 错误码={code}, 错误信息={info}")

        # 记录错误信息
        if not hasattr(context, 'error_log'):
            context.error_log = []

        error_info = {
            'time': context.now,
            'error_code': code,
            'error_info': info
        }
        context.error_log.append(error_info)

        # 增加错误计数
        context.error_count += 1

        # 根据错误类型进行相应处理
        if code in [10001, 10002]:  # 网络连接错误
            print(f"[{context.now}] 网络连接异常，请检查网络状态")
        elif code in [20001, 20002]:  # 交易相关错误
            print(f"[{context.now}] 交易执行异常，请检查账户状态")
        elif code in [30001, 30002]:  # 数据相关错误
            print(f"[{context.now}] 数据获取异常，可能影响策略执行")

    except Exception as e:
        print(f"[{context.now}] 错误处理函数异常: {e}")

def add_trade_log(context, symbol, side, price, volume, profit=0, profit_ratio=0, reason=""):
    """添加交易日志"""
    trade_info = {
        'time': context.now,
        'symbol': symbol,
        'side': side,
        'price': price,
        'volume': volume,
        'amount': price * volume,
        'profit': profit,
        'profit_ratio': profit_ratio,
        'reason': reason
    }
    context.trade_log.append(trade_info)

def on_backtest_finished(context, indicator):
    """回测结束 - 基于模板的统计报告"""
    print("\n" + "="*70)
    print("                           回测完成")
    print("="*70)

    # 交易统计
    buy_count = len([t for t in context.trade_log if t['side'] == "买入"])
    sell_count = len([t for t in context.trade_log if t['side'] == "卖出"])
    profit_trades = [t for t in context.trade_log if t['profit'] > 0]
    loss_trades = [t for t in context.trade_log if t['profit'] < 0]
    total_profit = sum([t['profit'] for t in context.trade_log])
    avg_profit = total_profit / max(1, sell_count)

    # 回测指标
    print(" 📊 回测指标:")
    print("-" * 70)
    print(f"总收益率: {indicator.pnl_ratio*100:.2f}%")
    print(f"年化收益率: {indicator.pnl_ratio_annual*100:.2f}%")
    print(f"最大回撤: {indicator.max_drawdown*100:.2f}%")
    print(f"夏普比率: {indicator.sharp_ratio:.2f}")
    print(f"交易次数: {buy_count+sell_count}次")
    print(f"胜率: {len(profit_trades)/max(1,len(profit_trades)+len(loss_trades))*100:.1f}%")

    print("="*70)
    print(" 📈 策略统计:")
    print(f"选股次数: {context.selection_count}")
    print(f"监控股票数量: {len(SYMBOLS)}")
    print(f"错误次数: {context.error_count}")

    # 最终持仓
    if context.positions:
        print("\n 💼 最终持仓:")
        for symbol, pos in context.positions.items():
            print(f"{symbol}: 买入价 {pos['price']:.2f}元")

    # 选中股票汇总
    print("\n" + "="*70)
    print("                        选中股票汇总")
    print("="*70)

    if hasattr(context, 'selected_stocks_details') and context.selected_stocks_details:
        print(f"📋 回测期间共选中 {len(context.selected_stocks_details)} 只股票:")
        print("-" * 90)
        print(f"{'股票代码':<12} {'选中日期':<12} {'涨幅':<8} {'市值(亿)':<8} {'换手率':<8} {'MA5':<8} {'MA10':<8} {'MA20':<8} {'RSI':<6} {'量比':<6}")
        print("-" * 90)

        for stock_info in context.selected_stocks_details:
            symbol = stock_info['symbol']
            date = stock_info.get('selection_date', stock_info.get('date', 'N/A'))  # 兼容新旧字段名
            price_change = stock_info.get('price_change', stock_info.get('intraday_change', 0))  # 兼容新旧字段名
            market_cap = stock_info.get('market_cap', 0) / 10000  # 转换为亿元
            turnover_rate = stock_info.get('turnover_rate', 0) * 100  # 转换为百分比
            ma5 = stock_info.get('ma5', 0)
            ma10 = stock_info.get('ma10', 0)
            ma20 = stock_info.get('ma20', 0)
            rsi = stock_info.get('rsi', 0)
            volume_ratio = stock_info.get('volume_ratio', 0)

            print(f"{symbol:<12} {date:<12} {price_change*100:>6.2f}% {market_cap:>7.1f} {turnover_rate:>7.2f}% {ma5:>7.2f} {ma10:>7.2f} {ma20:>7.2f} {rsi:>5.1f} {volume_ratio:>5.2f}")

        print("-" * 90)

        # 统计分析
        if len(context.selected_stocks_details) > 0:
            avg_change = sum([s.get('price_change', s.get('intraday_change', 0)) for s in context.selected_stocks_details]) / len(context.selected_stocks_details)
            avg_market_cap = sum([s.get('market_cap', 0) for s in context.selected_stocks_details if s.get('market_cap', 0) > 0])
            avg_market_cap = avg_market_cap / max(1, len([s for s in context.selected_stocks_details if s.get('market_cap', 0) > 0])) / 10000
            avg_turnover = sum([s.get('turnover_rate', 0) for s in context.selected_stocks_details if s.get('turnover_rate', 0) > 0])
            avg_turnover = avg_turnover / max(1, len([s for s in context.selected_stocks_details if s.get('turnover_rate', 0) > 0])) * 100
            avg_volume_ratio = sum([s.get('volume_ratio', 0) for s in context.selected_stocks_details if s.get('volume_ratio', 0) > 0])
            avg_volume_ratio = avg_volume_ratio / max(1, len([s for s in context.selected_stocks_details if s.get('volume_ratio', 0) > 0]))
            avg_rsi = sum([s.get('rsi', 0) for s in context.selected_stocks_details if s.get('rsi', 0) > 0])
            avg_rsi = avg_rsi / max(1, len([s for s in context.selected_stocks_details if s.get('rsi', 0) > 0]))

            print(f"📊 选股指标平均值:")
            print(f"   平均涨幅: {avg_change*100:.2f}%")
            print(f"   平均市值: {avg_market_cap:.1f}亿")
            print(f"   平均换手率: {avg_turnover:.2f}%")
            print(f"   平均量比: {avg_volume_ratio:.2f}倍")
            print(f"   平均RSI: {avg_rsi:.1f}")
    else:
        print("📋 回测期间未选中任何股票")

    print("\n" + "="*70)
    print("                        交易统计报告")
    print("="*70)
    print(f"总交易次数: {buy_count + sell_count}")
    print(f"盈利次数: {len(profit_trades)}")
    print(f"亏损次数: {len(loss_trades)}")
    print(f"胜率: {len(profit_trades)/max(1,len(profit_trades)+len(loss_trades))*100:.2f}%")
    print(f"总盈亏: {total_profit:.2f}元")
    print(f"平均每笔盈亏: {avg_profit:.2f}元")
    print("="*70)

# ==================== 主程序 ====================
if __name__ == '__main__':
    """
    策略入口 - 符合掘金SDK标准规范

    支持的运行模式：
    - MODE_BACKTEST: 回测模式
    - MODE_LIVE: 实时模式（仿真/实盘）
    """
    print("="*70)
    print("                全市场日内选股策略")
    print("="*70)

    # 运行策略（token在run函数中传递，符合掘金SDK规范）
    run(strategy_id=STRATEGY_ID,
        filename='main.py',
        mode=MODE_BACKTEST,  # 可修改为MODE_LIVE进行实时交易
        token=TOKEN,
        backtest_start_time=f'{BACKTEST_START_DATE} 09:30:00',
        backtest_end_time=f'{BACKTEST_END_DATE} 15:30:00',
        backtest_adjust=ADJUST_PREV,
        backtest_initial_cash=INITIAL_CASH,
        backtest_commission_ratio=0.0003,
        backtest_slippage_ratio=0.0001)
