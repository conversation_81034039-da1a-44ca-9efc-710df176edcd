# coding=utf-8
from __future__ import print_function, absolute_import
from gm.api import *
import datetime
import pandas as pd


def init(context):
    # 短线选股策略参数
    context.limit_up_threshold = 0.0976  # 涨停阈值 9.76%
    context.volume_shrink_ratio = 0.68   # 缩量比例 68%

    # 初始化选股结果存储
    context.all_results = []
    context.realtime_results = []

    # 每日定时任务：收盘后执行选股
    schedule(schedule_func=select_stocks, date_rule='1d', time_rule='15:30:00')

    # 实时监控任务：盘中检查实时状态（在函数内判断日期）
    schedule(schedule_func=check_realtime_status, date_rule='1d', time_rule='14:30:00')


def select_stocks(context):
    """短线选股主函数：1涨停2放量3回调4缩量"""
    current_date = context.now.strftime('%Y-%m-%d')
    print(f'{current_date}: 开始执行短线选股策略')

    # 获取全市场A股股票池
    all_stocks = get_all_stocks(context, current_date)
    if not all_stocks:
        print('未获取到股票池，跳过本次选股')
        return

    print(f'全市场股票池数量: {len(all_stocks)}')

    # 执行4天选股逻辑
    selected_stocks = []
    for symbol in all_stocks:
        if check_four_day_pattern(context, symbol, current_date):
            selected_stocks.append(symbol)

    # 输出选股结果并保存
    if selected_stocks:
        print(f'选出股票数量: {len(selected_stocks)}')
        for stock in selected_stocks:
            stock_name = get_stock_name(stock)
            print(f'选中股票: {stock} {stock_name}')
            # 保存到结果列表
            context.all_results.append({
                'date': current_date,
                'symbol': stock,
                'name': stock_name
            })
    else:
        print('本次未选出符合条件的股票')

    # 保存结果到CSV文件
    save_results_to_csv(context)


def check_realtime_status(context):
    """实时检查股票状态"""
    current_date = context.now.strftime('%Y-%m-%d')

    # 只在最新日期执行实时检查
    if current_date != '2025-09-04':
        print(f'{current_date}: 跳过实时检查（非最新日期）')
        return

    print(f'{current_date}: 开始实时状态检查')

    # 先找出9月1日涨停的股票作为候选池
    print('正在获取9月1日涨停股票...')

    # 获取全市场股票池
    all_stocks = get_all_stocks(context, '2025-09-01')
    if not all_stocks:
        print('未获取到股票池')
        return

    # 找出9月1日涨停的股票
    limit_up_stocks = []
    for symbol in all_stocks[:100]:  # 限制数量避免太多输出
        if check_limit_up_on_date(context, symbol, '2025-09-01'):
            limit_up_stocks.append(symbol)

    print(f'9月1日涨停股票数量: {len(limit_up_stocks)}')
    print(f'涨停股票前10只: {limit_up_stocks[:10]}')

    # 在涨停股票中检查实时满足条件的
    realtime_candidates = []
    for symbol in limit_up_stocks[:10]:  # 只测试前10只
        print(f'\n=== 测试股票 {symbol} ===')
        if check_realtime_pattern(context, symbol, current_date):
            realtime_candidates.append(symbol)

    # 输出实时检查结果
    if realtime_candidates:
        print(f'实时满足条件股票数量: {len(realtime_candidates)}')
        for stock in realtime_candidates:
            stock_name = get_stock_name(stock)
            print(f'实时候选股票: {stock} {stock_name}')
            # 保存到实时结果列表
            context.realtime_results.append({
                'date': current_date,
                'symbol': stock,
                'name': stock_name,
                'check_time': context.now.strftime('%H:%M:%S')
            })
    else:
        print('实时未发现满足条件的股票')

    # 保存实时结果到CSV文件
    save_realtime_results_to_csv(context)





def get_all_stocks(context, trade_date):
    """获取全市场A股股票池"""
    try:
        # 获取A股股票，过滤停牌、ST股票
        stocks_info = get_symbols(sec_type1=1010, sec_type2=101001,
                                skip_suspended=True, skip_st=True,
                                trade_date=trade_date, df=True)

        # 过滤上市时间不足的股票（至少上市5天）
        current_dt = pd.Timestamp(trade_date).tz_localize(None)
        # 处理时区问题
        stocks_info['listed_date_clean'] = stocks_info['listed_date'].dt.tz_localize(None)
        stocks_info = stocks_info[stocks_info['listed_date_clean'] <= current_dt - pd.Timedelta(days=5)]

        # 排除科创板和创业板（只保留主板）
        # board字段：10100101=主板，10100102=创业板，10100103=科创板，10100104=北交所
        stocks_info = stocks_info[stocks_info['board'] == 10100101]

        return stocks_info['symbol'].tolist()
    except Exception as e:
        print(f'获取股票池失败: {e}')
        return []


def get_stock_name(symbol):
    """获取股票名称"""
    try:
        stock_info = get_symbol_infos(sec_type1=1010, symbols=symbol)
        if stock_info:
            return stock_info[0]['sec_name']
        return ''
    except:
        return ''


def save_results_to_csv(context):
    """保存选股结果到CSV文件"""
    if context.all_results:
        try:
            import os
            df = pd.DataFrame(context.all_results)
            filename = 'stock_selection_results.csv'
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f'选股结果已保存到: {filename}')
        except Exception as e:
            print(f'保存CSV文件失败: {e}')


def save_realtime_results_to_csv(context):
    """保存实时检查结果到CSV文件"""
    if context.realtime_results:
        try:
            import os
            df = pd.DataFrame(context.realtime_results)
            filename = 'realtime_candidates.csv'
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f'实时候选结果已保存到: {filename}')
        except Exception as e:
            print(f'保存实时CSV文件失败: {e}')


def check_four_day_pattern(context, symbol, current_date):
    """检查4天选股模式：1涨停2放量3回调4缩量（当前日期为第4天）"""
    try:
        # 获取最近4天的历史数据（当前日期为第4天）
        hist_data = history_n(symbol=symbol, frequency='1d', count=4,
                            end_time=current_date,
                            fields='close,pre_close,amount',
                            skip_suspended=True, fill_missing='Last',
                            adjust=ADJUST_PREV, df=True)

        # 检查数据完整性
        if len(hist_data) < 4 or 'amount' not in hist_data.columns:
            return False

        # 获取4天数据（按时间顺序）
        day1_data = hist_data.iloc[0]   # 第1天（涨停日）
        day2_data = hist_data.iloc[1]   # 第2天（放量日）
        day3_data = hist_data.iloc[2]   # 第3天（回调日）
        day4_data = hist_data.iloc[3]   # 第4天（缩量日，当前日）

        # 条件1：第1天涨停（涨幅≥9.76%）
        day1_return = (day1_data['close'] - day1_data['pre_close']) / day1_data['pre_close']
        if day1_return < context.limit_up_threshold:
            return False

        # 条件2：第2天放量（成交金额>第1天）
        if day2_data['amount'] <= day1_data['amount']:
            return False

        # 条件3：第3天回调（收盘价下跌）
        if day3_data['close'] >= day2_data['close']:
            return False

        # 条件4：第4天缩量（成交金额≤第1天×0.68）
        if day4_data['amount'] > day1_data['amount'] * context.volume_shrink_ratio:
            return False

        return True

    except Exception as e:
        return False


def check_realtime_pattern(context, symbol, current_date):
    """实时检查3天模式+当前tick状态：1涨停2放量3回调+实时缩量"""
    try:
        # 获取前3天的历史数据
        hist_data = history_n(symbol=symbol, frequency='1d', count=3,
                            end_time=current_date,
                            fields='close,pre_close,amount',
                            skip_suspended=True, fill_missing='Last',
                            adjust=ADJUST_PREV, df=True)

        # 检查历史数据完整性
        if len(hist_data) < 3 or 'amount' not in hist_data.columns:
            return False

        # 获取3天历史数据
        day1_data = hist_data.iloc[0]   # 第1天（涨停日）
        day2_data = hist_data.iloc[1]   # 第2天（放量日）
        day3_data = hist_data.iloc[2]   # 第3天（回调日）

        # 检查前3天条件
        # 条件1：第1天涨停（涨幅≥9.76%）
        day1_return = (day1_data['close'] - day1_data['pre_close']) / day1_data['pre_close']
        if day1_return < context.limit_up_threshold:
            return False

        # 条件2：第2天放量（成交金额>第1天）
        if day2_data['amount'] <= day1_data['amount']:
            return False

        # 条件3：第3天回调（收盘价下跌）
        if day3_data['close'] >= day2_data['close']:
            return False

        # 条件4：实时检查当前成交金额是否缩量
        current_tick = current(symbols=symbol)

        # 打印tick数据调试信息
        print(f'\n{symbol} tick数据调试:')
        print(f'current()返回结果: {current_tick}')

        if not current_tick:
            print(f'❌ {symbol} 未获取到tick数据')
            return False

        tick_data = current_tick[0]
        print(f'tick数据字段: {list(tick_data.keys()) if hasattr(tick_data, "keys") else "非字典类型"}')
        print(f'tick数据内容: {tick_data}')

        # 尝试多种字段获取当前累计成交金额
        current_amount = 0
        amount_fields = ['cum_amount', 'amount', 'cum_volume', 'volume']

        for field in amount_fields:
            if hasattr(tick_data, field):
                current_amount = getattr(tick_data, field, 0)
                print(f'使用字段 {field}: {current_amount}')
                break
            elif isinstance(tick_data, dict) and field in tick_data:
                current_amount = tick_data[field]
                print(f'使用字段 {field}: {current_amount}')
                break

        if current_amount <= 0:
            print(f'❌ {symbol} 当前成交金额为0或无效: {current_amount}')
            return False

        # 判断当前成交金额是否≤第1天×0.68
        threshold = day1_data['amount'] * context.volume_shrink_ratio
        print(f'缩量判断: 当前{current_amount} vs 阈值{threshold:.0f}')

        if current_amount > threshold:
            print(f'❌ {symbol} 未缩量: {current_amount} > {threshold:.0f}')
            return False

        print(f'✅ {symbol} 实时满足缩量条件')
        return True

    except Exception as e:
        return False


def check_limit_up_on_date(context, symbol, target_date):
    """检查指定日期是否涨停"""
    try:
        # 获取目标日期的数据
        hist_data = history_n(symbol=symbol, frequency='1d', count=1,
                            end_time=target_date,
                            fields='close,pre_close',
                            skip_suspended=True, fill_missing='Last',
                            adjust=ADJUST_PREV, df=True)

        if len(hist_data) < 1:
            return False

        data = hist_data.iloc[0]
        # 计算涨幅
        return_rate = (data['close'] - data['pre_close']) / data['pre_close']

        # 判断是否涨停（≥9.76%）
        return return_rate >= context.limit_up_threshold

    except Exception as e:
        return False


if __name__ == '__main__':

    run(strategy_id='ea77b198-889a-11f0-b60e-00e2699251ab',
        filename='main.py',
        mode=MODE_BACKTEST,
        token='787cec949649eb140ea4ccccb14523af76110c8f',
        backtest_start_time='2025-07-01 08:00:00',
        backtest_end_time='2025-09-04 16:00:00',
        backtest_adjust=ADJUST_PREV,
        backtest_initial_cash=10000000,
        backtest_commission_ratio=0.0001,
        backtest_slippage_ratio=0.0001,
        backtest_match_mode=1)

