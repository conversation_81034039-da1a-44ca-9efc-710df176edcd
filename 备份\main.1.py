# coding=utf-8
from __future__ import print_function, absolute_import
from gm.api import *
import datetime
import pandas as pd


def init(context):
    # 短线选股策略参数
    context.limit_up_threshold = 0.0976  # 涨停阈值 9.76%
    context.volume_shrink_ratio = 0.68   # 缩量比例 68%

    # 每日定时任务：收盘后执行选股
    schedule(schedule_func=select_stocks, date_rule='1d', time_rule='15:30:00')


def select_stocks(context):
    """短线选股主函数：1涨停2放量3回调4缩量"""
    current_date = context.now.strftime('%Y-%m-%d')
    print(f'{current_date}: 开始执行短线选股策略')

    # 先获取4天前涨停的股票作为候选池
    limit_up_stocks = get_limit_up_stocks_4_days_ago(context, current_date)
    if not limit_up_stocks:
        print('未找到4天前涨停的股票，跳过本次选股')
        return

    print(f'4天前涨停股票数量: {len(limit_up_stocks)}')

    # 在涨停股票中执行4天选股逻辑
    selected_stocks = []

    # 特别检查指定股票 - 直接添加到股票池进行测试
    test_symbol = 'SZSE.002115'
    if test_symbol not in limit_up_stocks:
        print(f'\n特别添加 {test_symbol} 到股票池进行测试分析')
        limit_up_stocks.append(test_symbol)

    for symbol in limit_up_stocks:
        print(f'\n正在检查股票: {symbol}')
        if check_four_day_pattern(context, symbol, current_date):
            selected_stocks.append(symbol)
            print(f'✅ {symbol} 符合条件，已添加到选中列表')
        else:
            print(f'❌ {symbol} 不符合条件')

    # 输出选股结果
    if selected_stocks:
        print(f'选出股票数量: {len(selected_stocks)}')
        for stock in selected_stocks:
            print(f'选中股票: {stock}')
    else:
        print('本次未选出符合条件的股票')

    # 汇总显示002115未被选择的原因
    test_symbol = 'SZSE.002115'
    print(f'\n=== {test_symbol} 未被选择原因汇总 ===')
    if test_symbol not in limit_up_stocks:
        print(f'原因: {test_symbol} 在{limit_up_date}未涨停，不在候选池中')
    elif test_symbol not in selected_stocks:
        print(f'原因: {test_symbol} 在候选池中但不符合4天选股条件')
        print('具体不符合的条件请查看上方详细数据分析')
    else:
        print(f'{test_symbol} 已被成功选中!')


def get_limit_up_stocks_4_days_ago(context, current_date):
    """获取涨停股票作为候选池（当前日期为第4天缩量日）"""
    try:
        # 获取前3个交易日，当前日期为第4天
        trading_dates = get_previous_n_trading_dates(exchange='SHSE', date=current_date, n=3)
        if len(trading_dates) < 3:
            print('无法获取前3个交易日')
            return []

        # get_previous_n_trading_dates返回按时间顺序排列的日期（从早到晚）
        limit_up_date = trading_dates[0]   # 第1天涨停日（最早）
        volume_up_date = trading_dates[1]  # 第2天放量日
        callback_date = trading_dates[2]   # 第3天回调日（昨天）

        print(f'涨停日期: {limit_up_date}（第1天）')
        print(f'放量日期: {volume_up_date}（第2天）')
        print(f'回调日期: {callback_date}（第3天）')
        print(f'缩量日期: {current_date}（第4天，当前日期）')

        # 获取涨停日全市场A股
        all_stocks = get_all_stocks(context, limit_up_date)
        if not all_stocks:
            return []

        # 筛选涨停日涨停的股票
        limit_up_stocks = []
        for symbol in all_stocks:
            if check_limit_up_on_date(context, symbol, limit_up_date):
                limit_up_stocks.append(symbol)

        print(f'{limit_up_date} 涨停股票数量: {len(limit_up_stocks)}')
        return limit_up_stocks

    except Exception as e:
        print(f'获取涨停股票失败: {e}')
        return []


def check_limit_up_on_date(context, symbol, target_date):
    """检查指定日期是否涨停"""
    try:
        # 获取目标日期的数据
        hist_data = history_n(symbol=symbol, frequency='1d', count=1,
                            end_time=target_date,
                            fields='close,pre_close',
                            skip_suspended=True, fill_missing='Last',
                            adjust=ADJUST_PREV, df=True)

        if len(hist_data) < 1:
            return False

        data = hist_data.iloc[0]
        # 计算涨幅
        return_rate = (data['close'] - data['pre_close']) / data['pre_close']

        # 判断是否涨停（≥9.76%）
        return return_rate >= context.limit_up_threshold

    except Exception as e:
        return False


def get_all_stocks(context, trade_date):
    """获取全市场A股股票池"""
    try:
        # 获取A股股票，过滤停牌、ST股票
        stocks_info = get_symbols(sec_type1=1010, sec_type2=101001,
                                skip_suspended=True, skip_st=True,
                                trade_date=trade_date, df=True)

        # 过滤上市时间不足的股票（至少上市5天）
        current_dt = pd.Timestamp(trade_date).tz_localize(None)
        # 处理时区问题
        stocks_info['listed_date_clean'] = stocks_info['listed_date'].dt.tz_localize(None)
        stocks_info = stocks_info[stocks_info['listed_date_clean'] <= current_dt - pd.Timedelta(days=5)]

        # 排除科创板和创业板（只保留主板）
        # board字段：10100101=主板，10100102=创业板，10100103=科创板，10100104=北交所
        stocks_info = stocks_info[stocks_info['board'] == 10100101]

        return stocks_info['symbol'].tolist()
    except Exception as e:
        print(f'获取股票池失败: {e}')
        return []


def check_four_day_pattern(context, symbol, current_date):
    """检查4天选股模式：1涨停2放量3回调4缩量（当前日期为第4天）"""
    print(f'\n开始检查 {symbol} 的4天模式...')
    try:
        # 获取最近4天的历史数据（当前日期为第4天）
        hist_data = history_n(symbol=symbol, frequency='1d', count=4,
                            end_time=current_date,
                            fields='close,pre_close,volume,amount',
                            skip_suspended=True, fill_missing='Last',
                            adjust=ADJUST_PREV, df=True)

        print(f'{symbol} 数据获取成功，数据长度: {len(hist_data)}')

        if len(hist_data) < 4:
            print(f'❌ {symbol} 数据不足4天: 只有{len(hist_data)}天数据')
            return False

        # 获取4天数据（按时间顺序）
        day1_data = hist_data.iloc[0]   # 第1天（涨停日）
        day2_data = hist_data.iloc[1]   # 第2天（放量日）
        day3_data = hist_data.iloc[2]   # 第3天（回调日）
        day4_data = hist_data.iloc[3]   # 第4天（缩量日，当前日）

        # 打印详细数据用于调试
        print(f'\n{symbol} 4天数据详情:')
        print(f'数据长度: {len(hist_data)}')
        print(f'数据字段: {list(hist_data.columns)}')
        print(f'数据索引: {list(hist_data.index)}')

        # 检查数据完整性
        if len(hist_data) < 4:
            print(f'❌ 数据不足4天: 只有{len(hist_data)}天数据')
            return False

        # 检查amount字段是否存在
        if 'amount' not in hist_data.columns:
            print(f'❌ 缺少amount字段: {list(hist_data.columns)}')
            return False

        try:
            print(f'第1天: 收盘价={day1_data["close"]:.2f}, 成交金额={day1_data["amount"]:.0f}, 涨幅={(day1_data["close"]-day1_data["pre_close"])/day1_data["pre_close"]:.2%}')
            print(f'第2天: 收盘价={day2_data["close"]:.2f}, 成交金额={day2_data["amount"]:.0f}, 金额比例={day2_data["amount"]/day1_data["amount"]:.2f}倍')
            print(f'第3天: 收盘价={day3_data["close"]:.2f}, 成交金额={day3_data["amount"]:.0f}, 回调幅度={(day3_data["close"]-day2_data["close"])/day2_data["close"]:.2%}')
            print(f'第4天: 收盘价={day4_data["close"]:.2f}, 成交金额={day4_data["amount"]:.0f}, 金额比例={day4_data["amount"]/day1_data["amount"]:.2f}倍')
        except Exception as e:
            print(f'❌ 数据打印失败: {e}')
            print(f'原始数据:\n{hist_data}')
            return False

        # 条件1：第1天涨停（涨幅≥9.76%）- 已在候选池中筛选过
        day1_return = (day1_data['close'] - day1_data['pre_close']) / day1_data['pre_close']
        if day1_return < context.limit_up_threshold:
            print(f'❌ 第1天涨幅不足: {day1_return:.2%} < {context.limit_up_threshold:.2%}')
            return False

        # 条件2：第2天放量（成交金额>第1天）
        if day2_data['amount'] <= day1_data['amount']:
            print(f'❌ 第2天未放量: {day2_data["amount"]:.0f} <= {day1_data["amount"]:.0f}')
            return False

        # 条件3：第3天回调（收盘价下跌）
        if day3_data['close'] >= day2_data['close']:
            print(f'❌ 第3天未回调: {day3_data["close"]:.2f} >= {day2_data["close"]:.2f}')
            return False

        # 条件4：第4天缩量（成交金额≤第1天×0.68）
        if day4_data['amount'] > day1_data['amount'] * context.volume_shrink_ratio:
            print(f'❌ 第4天未缩量: {day4_data["amount"]:.0f} > {day1_data["amount"] * context.volume_shrink_ratio:.0f}')
            return False

        print(f'✅ {symbol} 符合4天选股条件!')

        return True

    except Exception as e:
        print(f'❌ {symbol} 数据获取失败: {e}')
        return False


if __name__ == '__main__':
    '''
        短线选股策略：1涨停2放量3回调4缩量
        strategy_id策略ID, 由系统生成
        filename文件名, 请与本文件名保持一致
        mode运行模式, 实时模式:MODE_LIVE回测模式:MODE_BACKTEST
        token绑定计算机的ID, 可在系统设置-密钥管理中生成
        backtest_start_time回测开始时间
        backtest_end_time回测结束时间
        backtest_adjust股票复权方式, 不复权:ADJUST_NONE前复权:ADJUST_PREV后复权:ADJUST_POST
        backtest_initial_cash回测初始资金
        backtest_commission_ratio回测佣金比例
        backtest_slippage_ratio回测滑点比例
        backtest_match_mode市价撮合模式，以下一tick/bar开盘价撮合:0，以当前tick/bar收盘价撮合：1
    '''
    run(strategy_id='ea77b198-889a-11f0-b60e-00e2699251ab',
        filename='main.py',
        mode=MODE_BACKTEST,
        token='787cec949649eb140ea4ccccb14523af76110c8f',
        backtest_start_time='2025-09-03 08:00:00',
        backtest_end_time='2025-09-03 16:00:00',
        backtest_adjust=ADJUST_PREV,
        backtest_initial_cash=10000000,
        backtest_commission_ratio=0.0001,
        backtest_slippage_ratio=0.0001,
        backtest_match_mode=1)

