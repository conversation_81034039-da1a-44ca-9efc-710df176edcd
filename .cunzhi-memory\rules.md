# 开发规范和规则

- 掘金量化SDK基础架构：使用from gm.api import *导入，策略结构为init()初始化→事件处理函数→run()执行，支持MODE_BACKTEST回测和MODE_LIVE实时两种模式
- 掘金策略开发规范：必须设置strategy_id和token，回测需要backtest_start_time/end_time，支持前复权ADJUST_PREV/后复权ADJUST_POST/不复权ADJUST_NONE，佣金和滑点通过ratio设置
- 掘金策略运行模板：run()函数必需参数包括strategy_id(策略ID)、filename(文件名)、mode(MODE_LIVE/MODE_BACKTEST)、token(绑定计算机ID)，回测需要backtest_start_time/end_time、backtest_adjust、backtest_initial_cash、backtest_commission_ratio、backtest_slippage_ratio、backtest_match_mode参数
- 掘金回测参数详解：backtest_match_mode撮合模式(0=下一tick/bar开盘价撮合，1=当前tick/bar收盘价撮合)，backtest_commission_ratio佣金比例，backtest_slippage_ratio滑点比例，backtest_initial_cash初始资金
- 掘金策略标准结构：必须包含init(context)初始化函数，可选on_bar/on_tick/on_order_status等事件处理函数，if __name__ == '__main__'下调用run()函数启动策略
- 掘金导入规范：from __future__ import print_function, absolute_import, unicode_literals确保Python2/3兼容性，from gm.api import *导入所有掘金API，常用第三方库import datetime, numpy as np, pandas as pd
- 掘金策略文件头部模板：# coding=utf-8指定文件编码，from __future__ import print_function, absolute_import确保兼容性，from gm.api import *导入掘金API，这是每个策略文件的标准开头
