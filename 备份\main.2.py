# coding=utf-8
from __future__ import print_function, absolute_import
from gm.api import *
import datetime
import pandas as pd


def init(context):
    # 短线选股策略参数
    context.limit_up_threshold = 0.0976  # 涨停阈值 9.76%
    context.volume_shrink_ratio = 0.68   # 缩量比例 68%

    # 每日定时任务：收盘后执行选股
    schedule(schedule_func=select_stocks, date_rule='1d', time_rule='15:30:00')


def select_stocks(context):
    """短线选股主函数：1涨停2放量3回调4缩量"""
    current_date = context.now.strftime('%Y-%m-%d')
    print(f'{current_date}: 开始执行短线选股策略')

    # 获取全市场A股股票池
    all_stocks = get_all_stocks(context, current_date)
    if not all_stocks:
        print('未获取到股票池，跳过本次选股')
        return

    print(f'全市场股票池数量: {len(all_stocks)}')

    # 执行4天选股逻辑
    selected_stocks = []
    for symbol in all_stocks:
        if check_four_day_pattern(context, symbol, current_date):
            selected_stocks.append(symbol)

    # 输出选股结果
    if selected_stocks:
        print(f'选出股票数量: {len(selected_stocks)}')
        for stock in selected_stocks:
            stock_name = get_stock_name(stock)
            print(f'选中股票: {stock} {stock_name}')
    else:
        print('本次未选出符合条件的股票')





def get_all_stocks(context, trade_date):
    """获取全市场A股股票池"""
    try:
        # 获取A股股票，过滤停牌、ST股票
        stocks_info = get_symbols(sec_type1=1010, sec_type2=101001,
                                skip_suspended=True, skip_st=True,
                                trade_date=trade_date, df=True)

        # 过滤上市时间不足的股票（至少上市5天）
        current_dt = pd.Timestamp(trade_date).tz_localize(None)
        # 处理时区问题
        stocks_info['listed_date_clean'] = stocks_info['listed_date'].dt.tz_localize(None)
        stocks_info = stocks_info[stocks_info['listed_date_clean'] <= current_dt - pd.Timedelta(days=5)]

        # 排除科创板和创业板（只保留主板）
        # board字段：10100101=主板，10100102=创业板，10100103=科创板，10100104=北交所
        stocks_info = stocks_info[stocks_info['board'] == 10100101]

        return stocks_info['symbol'].tolist()
    except Exception as e:
        print(f'获取股票池失败: {e}')
        return []


def get_stock_name(symbol):
    """获取股票名称"""
    try:
        stock_info = get_symbol_infos(sec_type1=1010, symbols=symbol)
        if stock_info:
            return stock_info[0]['sec_name']
        return ''
    except:
        return ''


def check_four_day_pattern(context, symbol, current_date):
    """检查4天选股模式：1涨停2放量3回调4缩量（当前日期为第4天）"""
    try:
        # 获取最近4天的历史数据（当前日期为第4天）
        hist_data = history_n(symbol=symbol, frequency='1d', count=4,
                            end_time=current_date,
                            fields='close,pre_close,amount',
                            skip_suspended=True, fill_missing='Last',
                            adjust=ADJUST_PREV, df=True)

        # 检查数据完整性
        if len(hist_data) < 4 or 'amount' not in hist_data.columns:
            return False

        # 获取4天数据（按时间顺序）
        day1_data = hist_data.iloc[0]   # 第1天（涨停日）
        day2_data = hist_data.iloc[1]   # 第2天（放量日）
        day3_data = hist_data.iloc[2]   # 第3天（回调日）
        day4_data = hist_data.iloc[3]   # 第4天（缩量日，当前日）

        # 条件1：第1天涨停（涨幅≥9.76%）
        day1_return = (day1_data['close'] - day1_data['pre_close']) / day1_data['pre_close']
        if day1_return < context.limit_up_threshold:
            return False

        # 条件2：第2天放量（成交金额>第1天）
        if day2_data['amount'] <= day1_data['amount']:
            return False

        # 条件3：第3天回调（收盘价下跌）
        if day3_data['close'] >= day2_data['close']:
            return False

        # 条件4：第4天缩量（成交金额≤第1天×0.68）
        if day4_data['amount'] > day1_data['amount'] * context.volume_shrink_ratio:
            return False

        return True

    except Exception as e:
        return False


if __name__ == '__main__':
    '''
        短线选股策略：1涨停2放量3回调4缩量
        strategy_id策略ID, 由系统生成
        filename文件名, 请与本文件名保持一致
        mode运行模式, 实时模式:MODE_LIVE回测模式:MODE_BACKTEST
        token绑定计算机的ID, 可在系统设置-密钥管理中生成
        backtest_start_time回测开始时间
        backtest_end_time回测结束时间
        backtest_adjust股票复权方式, 不复权:ADJUST_NONE前复权:ADJUST_PREV后复权:ADJUST_POST
        backtest_initial_cash回测初始资金
        backtest_commission_ratio回测佣金比例
        backtest_slippage_ratio回测滑点比例
        backtest_match_mode市价撮合模式，以下一tick/bar开盘价撮合:0，以当前tick/bar收盘价撮合：1
    '''
    run(strategy_id='ea77b198-889a-11f0-b60e-00e2699251ab',
        filename='main.py',
        mode=MODE_BACKTEST,
        token='787cec949649eb140ea4ccccb14523af76110c8f',
        backtest_start_time='2025-09-03 08:00:00',
        backtest_end_time='2025-09-03 16:00:00',
        backtest_adjust=ADJUST_PREV,
        backtest_initial_cash=10000000,
        backtest_commission_ratio=0.0001,
        backtest_slippage_ratio=0.0001,
        backtest_match_mode=1)

