# 常用模式和最佳实践

- 掘金数据订阅模式：subscribe(symbols, frequency, count)订阅数据，on_bar(context, bars)处理bar数据，context.data()获取历史数据窗口，支持1d/60s等多种频率
- 掘金交易函数：order_volume()按量委托，order_target_percent()按比例调仓，order_target_value()按金额调仓，支持OrderSide_Buy/Sell和PositionEffect_Open/Close
- 掘金最新API(gm3.0.148+)：get_symbol_infos()查询标的基本信息，get_symbols()查询交易日标的信息，get_trading_dates_by_year()查询交易日历，get_contract_expire_rest_days()查询合约到期天数
- 掘金账户查询：context.account().positions()查询全部持仓，context.account().position(symbol, side)查询指定持仓，context.account().cash查询资金，get_orders()查询委托
- 掘金事件处理：on_bar()处理bar数据，on_tick()处理tick数据，on_order_status()处理委托状态，on_execution_report()处理成交回报，schedule()设置定时任务
- 掘金高效数据处理：在init()中使用get_history_instruments()一次性获取全部数据并建立字典索引，避免在on_bar()中重复调用API，提高回测效率
- 掘金定时任务模式：schedule(schedule_func=algo, date_rule='1d', time_rule='09:30:00')设置每日定时任务，在指定时间执行algo函数，支持1d/1w/1m频率
- 掘金交易日查询：get_previous_n_trading_dates(exchange='SHSE', date=now_str, n=1)获取前n个交易日，配合pd.Timestamp判断月份变化实现月度调仓
- 掘金历史数据查询：history_n(symbol, frequency='1d', count=days+1, fields='close,bob', fill_missing='Last', adjust=ADJUST_PREV, end_time=last_day, df=True)获取指定数量的历史数据
- 掘金股票数据API：stk_get_index_constituents(index, trade_date)获取指数成分股，stk_get_daily_mktvalue_pt(symbols, fields='tot_mv', trade_date, df=True)获取股票市值数据
- 掘金股票筛选：get_symbols(sec_type1=1010, symbols=symbols, trade_date=now_str, skip_suspended=True, skip_st=True)过滤停牌和ST股票，检查上市退市日期
- 掘金仓位管理：get_position()获取当前所有持仓，order_target_percent(symbol, percent=0)平仓，order_target_percent(symbol, percent, order_type=OrderType_Limit, position_side=PositionSide_Long, price)开仓
- 掘金价格获取：history_n(symbol, frequency='1d', count=1, end_time=now_str, fields='open')获取开盘价，current(symbols=symbol)[0]['price']获取当前价（实时模式最新tick，回测模式最近分钟收盘价）
- 掘金委托状态处理：on_order_status(context, order)处理委托状态变化，order['status']==3表示全部成交，order['side']表示买卖方向，order['position_effect']表示开平仓类型
- 掘金策略模式：风格轮动策略使用定时任务+月度调仓，通过指数收益率比较选择最佳风格，按市值排序选股，使用限价单交易，预留2%资金防止手续费不足
- 掘金A股筛选函数：get_symbols(sec_type1=1010, sec_type2=101001, skip_suspended=True, skip_st=True, trade_date=date, df=True)获取A股代码，通过listed_date和delisted_date过滤次新股和退市股
- 掘金时间处理：pd.Timestamp(date).replace(tzinfo=None)去除时区信息，datetime.timedelta(days=new_days)计算时间差，用于过滤次新股（默认365天）
- 掘金市值排序：stk_get_daily_mktvalue_pt(symbols, fields='tot_mv', trade_date, df=True).sort_values(by='tot_mv')获取市值并排序，fundamental.iloc[:num,:]选择前N只股票
- 掘金收盘价交易：history_n(symbol, frequency='1d', count=1, end_time=now_str, fields='close')获取收盘价进行限价交易，与开盘价交易形成对比
- 掘金自定义函数模式：get_normal_stocks(date, new_days=365, skip_suspended=True, skip_st=True)封装股票筛选逻辑，返回股票列表和字符串格式，提高代码复用性
- 掘金策略对比：小市值策略vs风格轮动策略 - 都使用月度调仓+定时任务，但小市值用收盘价交易(15:00)，风格轮动用开盘价交易(09:30)，选股逻辑不同
- 掘金财务数据API：stk_get_fundamentals_balance_pt(symbols, date, fields='ttl_eqy', df=True)获取资产负债表数据，通过groupby和max筛选最新财报数据
- 掘金数据合并处理：使用pandas.merge(on=['symbol'], how='left')合并市值和财务数据，计算PB=市值/净资产，账面市值比=PB的倒数
- 掘金分位数计算：使用DataFrame.quantile(0.50)计算50%分位点，quantile(0.30)和quantile(0.70)计算30%和70%分位点，用于股票分类
- 掘金收益率计算：close[-1]/close[0]-1计算区间收益率，配合history_n获取指定期间的收盘价数据，支持skip_suspended和fill_missing参数
- 掘金数据类型处理：使用astype(np.float64)统一数据类型，pd.concat()合并DataFrame，set_index()设置索引，确保数据计算的准确性
- 掘金线性回归：使用np.linalg.lstsq(x_value.T, y_value, rcond=None)进行OLS回归，计算alpha系数，x_value包含市场收益率、SMB、HML和常数项
- 掘金市值加权计算：自定义market_value_weighted函数，通过mv/np.sum(mv)计算权重，return*权重计算加权收益率，实现组合收益率计算
- 掘金因子模型策略：Fama-French三因子模型实现，包括SMB(小市值-大市值)、HML(高BM-低BM)因子计算，通过alpha<0筛选被低估股票
- 掘金股票选择逻辑：data_df[data_df.alpha < 0].sort_values(by='alpha').head(10)选择alpha最小且小于0的前10只股票，体现价值投资理念
- 掘金高频数据订阅：subscribe(symbols, frequency='60s', count=100, fields='symbol,eob,close')订阅1分钟bar数据，支持多标的同时订阅
- 掘金市价单交易：order_volume(symbol, volume, side=OrderSide_Buy, order_type=OrderType_Market, position_effect=PositionEffect_Open)使用市价单快速成交
- 掘金持仓查询：list(filter(lambda x:x['symbol']==symbol, get_position()))筛选特定标的持仓，position[0]['available_today']获取今日可用仓位
- 掘金时间判断：context.now.hour和context.now.minute判断具体时间，实现尾盘回转逻辑(14:57-15:00)，支持精确的时间控制
- 掘金目标仓位调整：order_target_volume(symbol, volume=context.total, order_type=OrderType_Market, position_side=PositionSide_Long)调整到目标持仓量
- 掘金技术指标计算：自定义EMA和MACD函数，使用pd.Series().ewm(span=N, adjust=False).mean()计算指数移动平均，实现技术分析
- 掘金信号判断：macd[-2]<=0 and macd[-1]>0判断金叉信号，macd[-2]>=0 and macd[-1]<0判断死叉信号，实现技术指标交易信号
- 掘金动态时间设置：datetime.datetime.now()-datetime.timedelta(days=180)动态计算回测时间，str()[:19]格式化时间字符串
- 掘金日内回转策略：建立底仓+MACD信号交易+尾盘回转，通过available_today控制T+0交易，维持总仓位不变降低成本
- 掘金外部数据集成：使用requests库爬取雪球网站数据，通过json.loads()解析JSON数据，实现外部投资组合跟随策略
- 掘金多线程处理：使用threading.Thread()创建多线程，通过t.start()启动和t.join()等待，实现并发数据获取提高效率
- 掘金重试机制：使用@retry装饰器实现网络请求重试，wait_random_min/max设置随机等待时间，stop_max_attempt_number设置最大重试次数
- 掘金动态订阅：通过subscribe(list(context.stocks_sub), frequency='1d', count=1, unsubscribe_previous=False)动态订阅股票，根据爬取的交易信息实时更新订阅列表
- 掘金时间戳处理：datetime.datetime.fromtimestamp(timestamp/1000)将毫秒时间戳转换为datetime对象，+datetime.timedelta(seconds=6)添加延迟
- 掘金委托拒绝处理：在on_order_status中处理order.status==8的拒绝情况，通过ord_rej_reason_detail判断拒绝原因，实现智能重试和降低委托量
- 掘金滑点分析：计算market_slippage=(order_price-target_price)/order_price和total_slippage=(filled_vwap-target_price)/order_price，分析交易成本
- 掘金数据持久化：使用pd.DataFrame.to_csv()保存回测结果，os.path.exists()检查文件存在性，pd.read_csv()读取历史数据实现数据累积
- 掘金跟随策略：雪球组合跟随策略通过爬虫获取调仓信息，多线程处理，动态订阅，智能委托处理，滑点分析和数据持久化的完整解决方案
- 掘金高频tick订阅：subscribe(symbols, frequency='tick', count=2, fields='symbol,eob,close', format='row')订阅tick数据，format='row'提供最高性能，支持多字段订阅
- 掘金Level2数据订阅：subscribe(symbols, frequency='l2transaction')订阅逐笔成交，subscribe(symbols, frequency='l2order')订阅逐笔委托，仅券商内网环境支持
- 掘金Level2事件处理：on_l2transaction(context, transition)处理逐笔成交事件，on_l2order(context, order)处理逐笔委托事件，毫秒级实时推送
- 掘金Level2历史数据：get_history_l2ticks()获取历史L2 Tick，get_history_l2transactions()获取历史逐笔成交，get_history_l2orders()获取历史逐笔委托，每次只能提取一天数据
- 掘金tick数据格式：format='row'(最高性能)、format='col'(列式)、format='df'(DataFrame默认)，row>col>df的性能顺序，fields指定字段越少查询越快
- 掘金高频数据特性：Level2数据延时更低、更新频率更快、十档行情深度更丰富，上交所和深交所都支持3秒tick和毫秒级逐笔数据，历史数据从2016年开始
- 掘金tick数据同步：wait_group=True等待同频率bar同时到齐，wait_group_timeout='10s'设置超时时间，unsubscribe_previous=True取消之前订阅
- 掘金history_n字段说明：支持fields='close,pre_close,volume,amount'等字段，amount为成交金额，volume为成交量，返回DataFrame时索引为日期，数据按eob升序排序
- 掘金实时tick数据获取：在实时模式使用current([symbol])获取实时价格，返回格式为[{'symbol': 'SHSE.600021', 'price': 19.24, 'created_at': datetime, ...}]，通过current_tick[0]['price']获取实时价格
- 掘金模式判断：context.mode == 1为实时模式，context.mode == 2为回测模式，在实时模式使用current()获取实时价格，回测模式使用history_n()获取收盘价
- 掘金实时数据处理最佳实践：实时模式中先尝试current()获取实时价格，如果失败则使用history_n()作为备选，确保数据获取的稳定性和容错性
- 掘金last_tick函数：last_tick(symbols, fields='', include_call_auction=False)查询已订阅的最新tick数据，必须先用subscribe()订阅tick行情，返回格式与current()相同但只返回已订阅标的的数据
- 掘金current函数详解：current(symbols, fields='', include_call_auction=False)返回list[dict]格式，包含cum_volume累计成交量、cum_amount累计成交金额、last_amount最新成交金额、last_volume最新成交量等字段
- 掘金回测模式tick限制：回测中current()只有symbol、price、created_at字段有效，其余字段为0；如需完整tick数据需先subscribe(frequency='tick')订阅，然后使用last_tick()或context.data()获取
